<!doctype html public "-//w3c//dtd html 4.0 transitional//en">
<html><head>
<title>Static Call Graph - [2025template\2025template.axf]</title></head>
<body><HR>
<H1>Static Call Graph for image 2025template\2025template.axf</H1><HR>
<BR><P>#&#060CALLGRAPH&#062# ARM Linker, 5060960: Last Updated: Sun Jul 27 16:43:59 2025
<BR><P>
<H3>Maximum Stack Usage =        928 bytes + Unknown(Functions without stacksize, Cycles, Untraceable Function Pointers)</H3><H3>
Call chain for Maximum Stack Depth:</H3>
uart_proc &rArr; parse_y_motor_data &rArr; my_printf &rArr; vsnprintf &rArr; _printf_char_common &rArr; __printf
<P>
<H3>
Functions with no stack information
</H3><UL>
 <LI><a href="#[df]">__user_initial_stackheap</a>
</UL>
</UL>
<P>
<H3>
Mutually Recursive functions
</H3> <LI><a href="#[27]">ADC_IRQHandler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[27]">ADC_IRQHandler</a><BR>
 <LI><a href="#[f]">BusFault_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[f]">BusFault_Handler</a><BR>
 <LI><a href="#[d]">HardFault_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[d]">HardFault_Handler</a><BR>
 <LI><a href="#[e]">MemManage_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[e]">MemManage_Handler</a><BR>
 <LI><a href="#[c]">NMI_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[c]">NMI_Handler</a><BR>
 <LI><a href="#[12d]">UART_EndRxTransfer</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[12d]">UART_EndRxTransfer</a><BR>
 <LI><a href="#[10]">UsageFault_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[10]">UsageFault_Handler</a><BR>
 <LI><a href="#[15e]">UART_EndTxTransfer</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[15e]">UART_EndTxTransfer</a><BR>
</UL>
<P>
<H3>
Function Pointers
</H3><UL>
 <LI><a href="#[27]">ADC_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[f]">BusFault_Handler</a> from stm32f4xx_it.o(i.BusFault_Handler) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[29]">CAN1_RX0_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[2a]">CAN1_RX1_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[2b]">CAN1_SCE_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[28]">CAN1_TX_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[55]">CAN2_RX0_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[56]">CAN2_RX1_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[57]">CAN2_SCE_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[54]">CAN2_TX_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[63]">DCMI_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[20]">DMA1_Stream0_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[21]">DMA1_Stream1_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[22]">DMA1_Stream2_IRQHandler</a> from stm32f4xx_it.o(i.DMA1_Stream2_IRQHandler) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[23]">DMA1_Stream3_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[24]">DMA1_Stream4_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[25]">DMA1_Stream5_IRQHandler</a> from stm32f4xx_it.o(i.DMA1_Stream5_IRQHandler) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[26]">DMA1_Stream6_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[44]">DMA1_Stream7_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[4d]">DMA2_Stream0_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[4e]">DMA2_Stream1_IRQHandler</a> from stm32f4xx_it.o(i.DMA2_Stream1_IRQHandler) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[4f]">DMA2_Stream2_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[50]">DMA2_Stream3_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[51]">DMA2_Stream4_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[59]">DMA2_Stream5_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[5a]">DMA2_Stream6_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[5b]">DMA2_Stream7_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[12]">DebugMon_Handler</a> from stm32f4xx_it.o(i.DebugMon_Handler) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[52]">ETH_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[53]">ETH_WKUP_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[1b]">EXTI0_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[3d]">EXTI15_10_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[1c]">EXTI1_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[1d]">EXTI2_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[1e]">EXTI3_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[1f]">EXTI4_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[2c]">EXTI9_5_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[19]">FLASH_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[45]">FMC_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[65]">FPU_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[64]">HASH_RNG_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[d]">HardFault_Handler</a> from stm32f4xx_it.o(i.HardFault_Handler) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[35]">I2C1_ER_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[34]">I2C1_EV_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[37]">I2C2_ER_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[36]">I2C2_EV_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[5e]">I2C3_ER_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[5d]">I2C3_EV_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[e]">MemManage_Handler</a> from stm32f4xx_it.o(i.MemManage_Handler) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[c]">NMI_Handler</a> from stm32f4xx_it.o(i.NMI_Handler) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[58]">OTG_FS_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[3f]">OTG_FS_WKUP_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[60]">OTG_HS_EP1_IN_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[5f]">OTG_HS_EP1_OUT_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[62]">OTG_HS_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[61]">OTG_HS_WKUP_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[16]">PVD_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[13]">PendSV_Handler</a> from stm32f4xx_it.o(i.PendSV_Handler) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[1a]">RCC_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[3e]">RTC_Alarm_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[18]">RTC_WKUP_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[b]">Reset_Handler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[46]">SDIO_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[38]">SPI1_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[39]">SPI2_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[48]">SPI3_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[11]">SVC_Handler</a> from stm32f4xx_it.o(i.SVC_Handler) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[14]">SysTick_Handler</a> from stm32f4xx_it.o(i.SysTick_Handler) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[66]">SystemInit</a> from system_stm32f4xx.o(i.SystemInit) referenced from startup_stm32f407xx.o(.text)
 <LI><a href="#[17]">TAMP_STAMP_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[2d]">TIM1_BRK_TIM9_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[30]">TIM1_CC_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[2f]">TIM1_TRG_COM_TIM11_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[2e]">TIM1_UP_TIM10_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[31]">TIM2_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[32]">TIM3_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[33]">TIM4_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[47]">TIM5_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[4b]">TIM6_DAC_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[4c]">TIM7_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[40]">TIM8_BRK_TIM12_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[43]">TIM8_CC_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[42]">TIM8_TRG_COM_TIM14_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[41]">TIM8_UP_TIM13_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[49]">UART4_IRQHandler</a> from stm32f4xx_it.o(i.UART4_IRQHandler) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[4a]">UART5_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[6f]">UART_DMAAbortOnError</a> from stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError) referenced from stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler)
 <LI><a href="#[74]">UART_DMAError</a> from stm32f4xx_hal_uart.o(i.UART_DMAError) referenced from stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA)
 <LI><a href="#[72]">UART_DMAReceiveCplt</a> from stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt) referenced from stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA)
 <LI><a href="#[73]">UART_DMARxHalfCplt</a> from stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt) referenced from stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA)
 <LI><a href="#[3a]">USART1_IRQHandler</a> from stm32f4xx_it.o(i.USART1_IRQHandler) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[3b]">USART2_IRQHandler</a> from stm32f4xx_it.o(i.USART2_IRQHandler) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[3c]">USART3_IRQHandler</a> from stm32f4xx_it.o(i.USART3_IRQHandler) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[5c]">USART6_IRQHandler</a> from stm32f4xx_it.o(i.USART6_IRQHandler) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[10]">UsageFault_Handler</a> from stm32f4xx_it.o(i.UsageFault_Handler) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[15]">WWDG_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[7c]">__main</a> from __main.o(!!!main) referenced from startup_stm32f407xx.o(.text)
 <LI><a href="#[6e]">_get_lc_ctype</a> from lc_ctype_c.o(locale$$code) referenced from rt_ctype_table.o(.text)
 <LI><a href="#[6b]">_printf_input_char</a> from _printf_char_common.o(.text) referenced from _printf_char_common.o(.text)
 <LI><a href="#[6a]">_sbackspace</a> from _sgetc.o(.text) referenced from __0sscanf.o(.text)
 <LI><a href="#[6c]">_scanf_char_input</a> from scanf_char.o(.text) referenced from scanf_char.o(.text)
 <LI><a href="#[69]">_sgetc</a> from _sgetc.o(.text) referenced from __0sscanf.o(.text)
 <LI><a href="#[68]">_snputc</a> from _snputc.o(.text) referenced from vsnprintf.o(.text)
 <LI><a href="#[79]">encoder_proc</a> from encoder_bsp.o(i.encoder_proc) referenced from schedule.o(.data)
 <LI><a href="#[7b]">gray_proc</a> from gray_bsp.o(i.gray_proc) referenced from schedule.o(.data)
 <LI><a href="#[6d]">isspace</a> from isspace.o(.text) referenced from scanf_char.o(.text)
 <LI><a href="#[7a]">key_proc</a> from key_bsp.o(i.key_proc) referenced from schedule.o(.data)
 <LI><a href="#[78]">motor_proc</a> from motor_bsp.o(i.motor_proc) referenced from schedule.o(.data)
 <LI><a href="#[75]">oled_proc</a> from oled_bsp.o(i.oled_proc) referenced from schedule.o(.data)
 <LI><a href="#[77]">pi_proc</a> from pi_bsp.o(i.pi_proc) referenced from schedule.o(.data)
 <LI><a href="#[70]">pid_param_init</a> from mypid.o(i.pid_param_init) referenced from mypid.o(i.PID_struct_init)
 <LI><a href="#[71]">pid_reset</a> from mypid.o(i.pid_reset) referenced from mypid.o(i.PID_struct_init)
 <LI><a href="#[76]">uart_proc</a> from uart_bsp.o(i.uart_proc) referenced from schedule.o(.data)
</UL>
<P>
<H3>
Global Symbols
</H3>
<P><STRONG><a name="[7c]"></a>__main</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, __main.o(!!!main))
<BR><BR>[Calls]<UL><LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry
<LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
</UL>

<P><STRONG><a name="[7d]"></a>__scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __scatter.o(!!!scatter))
<BR><BR>[Called By]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main
</UL>

<P><STRONG><a name="[7f]"></a>__scatterload_rt2</STRONG> (Thumb, 44 bytes, Stack size unknown bytes, __scatter.o(!!!scatter), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry
</UL>

<P><STRONG><a name="[17c]"></a>__scatterload_rt2_thumb_only</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __scatter.o(!!!scatter), UNUSED)

<P><STRONG><a name="[17d]"></a>__scatterload_null</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __scatter.o(!!!scatter), UNUSED)

<P><STRONG><a name="[80]"></a>__scatterload_copy</STRONG> (Thumb, 26 bytes, Stack size unknown bytes, __scatter_copy.o(!!handler_copy), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload_copy
</UL>
<BR>[Called By]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload_copy
</UL>

<P><STRONG><a name="[17e]"></a>__scatterload_zeroinit</STRONG> (Thumb, 28 bytes, Stack size unknown bytes, __scatter_zi.o(!!handler_zi), UNUSED)

<P><STRONG><a name="[81]"></a>_printf_n</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_n.o(.ARM.Collect$$_printf_percent$$00000001))
<BR><BR>[Calls]<UL><LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_charcount
</UL>

<P><STRONG><a name="[bd]"></a>_printf_percent</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__printf
</UL>

<P><STRONG><a name="[83]"></a>_printf_p</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_p.o(.ARM.Collect$$_printf_percent$$00000002))
<BR><BR>[Stack]<UL><LI>Max Depth = 64 + Unknown Stack Size
<LI>Call Chain = _printf_p &rArr; _printf_hex_ptr &rArr; _printf_longlong_hex &rArr; _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_hex_ptr
</UL>

<P><STRONG><a name="[85]"></a>_printf_f</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_f.o(.ARM.Collect$$_printf_percent$$00000003))
<BR><BR>[Stack]<UL><LI>Max Depth = 324 + Unknown Stack Size
<LI>Call Chain = _printf_f &rArr; _printf_fp_dec &rArr; _printf_fp_dec_real &rArr; _fp_digits &rArr; _btod_etento &rArr; _btod_emul &rArr; _e2e
</UL>
<BR>[Calls]<UL><LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_dec
</UL>

<P><STRONG><a name="[87]"></a>_printf_e</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_e.o(.ARM.Collect$$_printf_percent$$00000004))
<BR><BR>[Stack]<UL><LI>Max Depth = 324 + Unknown Stack Size
<LI>Call Chain = _printf_e &rArr; _printf_fp_dec &rArr; _printf_fp_dec_real &rArr; _fp_digits &rArr; _btod_etento &rArr; _btod_emul &rArr; _e2e
</UL>
<BR>[Calls]<UL><LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_dec
</UL>

<P><STRONG><a name="[88]"></a>_printf_g</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_g.o(.ARM.Collect$$_printf_percent$$00000005))
<BR><BR>[Stack]<UL><LI>Max Depth = 324 + Unknown Stack Size
<LI>Call Chain = _printf_g &rArr; _printf_fp_dec &rArr; _printf_fp_dec_real &rArr; _fp_digits &rArr; _btod_etento &rArr; _btod_emul &rArr; _e2e
</UL>
<BR>[Calls]<UL><LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_dec
</UL>

<P><STRONG><a name="[89]"></a>_printf_a</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_a.o(.ARM.Collect$$_printf_percent$$00000006))
<BR><BR>[Stack]<UL><LI>Max Depth = 112 + Unknown Stack Size
<LI>Call Chain = _printf_a &rArr; _printf_fp_hex &rArr; _printf_fp_hex_real &rArr; _printf_fp_infnan &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_hex
</UL>

<P><STRONG><a name="[17f]"></a>_printf_ll</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007))

<P><STRONG><a name="[8b]"></a>_printf_i</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_i.o(.ARM.Collect$$_printf_percent$$00000008))
<BR><BR>[Stack]<UL><LI>Max Depth = 72 + Unknown Stack Size
<LI>Call Chain = _printf_i &rArr; _printf_int_dec &rArr; _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_dec
</UL>

<P><STRONG><a name="[8d]"></a>_printf_d</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_d.o(.ARM.Collect$$_printf_percent$$00000009))
<BR><BR>[Stack]<UL><LI>Max Depth = 72 + Unknown Stack Size
<LI>Call Chain = _printf_d &rArr; _printf_int_dec &rArr; _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_dec
</UL>

<P><STRONG><a name="[8e]"></a>_printf_u</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A))
<BR><BR>[Stack]<UL><LI>Max Depth = 72 + Unknown Stack Size
<LI>Call Chain = _printf_u &rArr; _printf_int_dec &rArr; _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_dec
</UL>

<P><STRONG><a name="[8f]"></a>_printf_o</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B))
<BR><BR>[Stack]<UL><LI>Max Depth = 64 + Unknown Stack Size
<LI>Call Chain = _printf_o &rArr; _printf_int_oct &rArr; _printf_longlong_oct &rArr; _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_oct
</UL>

<P><STRONG><a name="[91]"></a>_printf_x</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C))
<BR><BR>[Stack]<UL><LI>Max Depth = 80 + Unknown Stack Size
<LI>Call Chain = _printf_x &rArr; _printf_int_hex &rArr; _printf_longlong_hex &rArr; _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_hex
</UL>

<P><STRONG><a name="[93]"></a>_printf_lli</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D))
<BR><BR>[Stack]<UL><LI>Max Depth = 72 + Unknown Stack Size
<LI>Call Chain = _printf_lli &rArr; _printf_longlong_dec &rArr; _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_longlong_dec
</UL>

<P><STRONG><a name="[95]"></a>_printf_lld</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E))
<BR><BR>[Stack]<UL><LI>Max Depth = 72 + Unknown Stack Size
<LI>Call Chain = _printf_lld &rArr; _printf_longlong_dec &rArr; _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_longlong_dec
</UL>

<P><STRONG><a name="[96]"></a>_printf_llu</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F))
<BR><BR>[Stack]<UL><LI>Max Depth = 72 + Unknown Stack Size
<LI>Call Chain = _printf_llu &rArr; _printf_longlong_dec &rArr; _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_longlong_dec
</UL>

<P><STRONG><a name="[97]"></a>_printf_llo</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010))
<BR><BR>[Stack]<UL><LI>Max Depth = 56 + Unknown Stack Size
<LI>Call Chain = _printf_llo &rArr; _printf_ll_oct &rArr; _printf_longlong_oct &rArr; _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_ll_oct
</UL>

<P><STRONG><a name="[99]"></a>_printf_llx</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011))
<BR><BR>[Stack]<UL><LI>Max Depth = 64 + Unknown Stack Size
<LI>Call Chain = _printf_llx &rArr; _printf_ll_hex &rArr; _printf_longlong_hex &rArr; _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_ll_hex
</UL>

<P><STRONG><a name="[180]"></a>_printf_l</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_l.o(.ARM.Collect$$_printf_percent$$00000012))

<P><STRONG><a name="[9b]"></a>_printf_c</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_c.o(.ARM.Collect$$_printf_percent$$00000013))
<BR><BR>[Stack]<UL><LI>Max Depth = 40 + Unknown Stack Size
<LI>Call Chain = _printf_c &rArr; _printf_char &rArr; _printf_cs_common &rArr; _printf_str &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_char
</UL>

<P><STRONG><a name="[9d]"></a>_printf_s</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_s.o(.ARM.Collect$$_printf_percent$$00000014))
<BR><BR>[Stack]<UL><LI>Max Depth = 40 + Unknown Stack Size
<LI>Call Chain = _printf_s &rArr; _printf_string &rArr; _printf_cs_common &rArr; _printf_str &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_string
</UL>

<P><STRONG><a name="[9f]"></a>_printf_lc</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015))
<BR><BR>[Stack]<UL><LI>Max Depth = 88 + Unknown Stack Size
<LI>Call Chain = _printf_lc &rArr; _printf_wchar &rArr; _printf_lcs_common &rArr; _printf_wctomb &rArr; _wcrtomb &rArr; __rt_ctype_table
</UL>
<BR>[Calls]<UL><LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_wchar
</UL>

<P><STRONG><a name="[a1]"></a>_printf_ls</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016))
<BR><BR>[Stack]<UL><LI>Max Depth = 88 + Unknown Stack Size
<LI>Call Chain = _printf_ls &rArr; _printf_wstring &rArr; _printf_lcs_common &rArr; _printf_wctomb &rArr; _wcrtomb &rArr; __rt_ctype_table
</UL>
<BR>[Calls]<UL><LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_wstring
</UL>

<P><STRONG><a name="[181]"></a>_printf_percent_end</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017))

<P><STRONG><a name="[ad]"></a>__rt_lib_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit.o(.ARM.Collect$$libinit$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_li
</UL>

<P><STRONG><a name="[a3]"></a>__rt_lib_init_fp_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000001))
<BR><BR>[Calls]<UL><LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_init
</UL>

<P><STRONG><a name="[182]"></a>__rt_lib_init_heap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000000A))

<P><STRONG><a name="[a5]"></a>__rt_lib_init_lc_common</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000000F))
<BR><BR>[Calls]<UL><LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_locale
</UL>

<P><STRONG><a name="[183]"></a>__rt_lib_init_preinit_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000004))

<P><STRONG><a name="[184]"></a>__rt_lib_init_rand_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000000E))

<P><STRONG><a name="[185]"></a>__rt_lib_init_user_alloc_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000000C))

<P><STRONG><a name="[186]"></a>__rt_lib_init_lc_collate_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000011))

<P><STRONG><a name="[a7]"></a>__rt_lib_init_lc_ctype_2</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000012))
<BR><BR>[Stack]<UL><LI>Max Depth = 8 + Unknown Stack Size
<LI>Call Chain = __rt_lib_init_lc_ctype_2 &rArr; _get_lc_ctype
</UL>
<BR>[Calls]<UL><LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_get_lc_ctype
</UL>

<P><STRONG><a name="[187]"></a>__rt_lib_init_lc_ctype_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000013))

<P><STRONG><a name="[188]"></a>__rt_lib_init_lc_monetary_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000015))

<P><STRONG><a name="[a8]"></a>__rt_lib_init_lc_numeric_2</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000016))
<BR><BR>[Stack]<UL><LI>Max Depth = 8 + Unknown Stack Size
<LI>Call Chain = __rt_lib_init_lc_numeric_2 &rArr; _get_lc_numeric
</UL>
<BR>[Calls]<UL><LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_get_lc_numeric
</UL>

<P><STRONG><a name="[189]"></a>__rt_lib_init_alloca_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000002E))

<P><STRONG><a name="[18a]"></a>__rt_lib_init_argv_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000002C))

<P><STRONG><a name="[18b]"></a>__rt_lib_init_atexit_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000001B))

<P><STRONG><a name="[18c]"></a>__rt_lib_init_clock_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000021))

<P><STRONG><a name="[18d]"></a>__rt_lib_init_cpp_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000032))

<P><STRONG><a name="[18e]"></a>__rt_lib_init_exceptions_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000030))

<P><STRONG><a name="[18f]"></a>__rt_lib_init_fp_trap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000001F))

<P><STRONG><a name="[190]"></a>__rt_lib_init_getenv_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000023))

<P><STRONG><a name="[191]"></a>__rt_lib_init_lc_numeric_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000017))

<P><STRONG><a name="[192]"></a>__rt_lib_init_lc_time_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000019))

<P><STRONG><a name="[193]"></a>__rt_lib_init_return</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000033))

<P><STRONG><a name="[194]"></a>__rt_lib_init_signal_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000001D))

<P><STRONG><a name="[195]"></a>__rt_lib_init_stdio_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000025))

<P><STRONG><a name="[b2]"></a>__rt_lib_shutdown</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown.o(.ARM.Collect$$libshutdown$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;abort
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_exit_ls
</UL>

<P><STRONG><a name="[196]"></a>__rt_lib_shutdown_cpp_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000004))

<P><STRONG><a name="[197]"></a>__rt_lib_shutdown_fini_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000002))

<P><STRONG><a name="[198]"></a>__rt_lib_shutdown_fp_trap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000009))

<P><STRONG><a name="[199]"></a>__rt_lib_shutdown_heap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000011))

<P><STRONG><a name="[19a]"></a>__rt_lib_shutdown_return</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000012))

<P><STRONG><a name="[19b]"></a>__rt_lib_shutdown_signal_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C))

<P><STRONG><a name="[19c]"></a>__rt_lib_shutdown_stdio_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000006))

<P><STRONG><a name="[19d]"></a>__rt_lib_shutdown_user_alloc_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E))

<P><STRONG><a name="[7e]"></a>__rt_entry</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry.o(.ARM.Collect$$rtentry$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload_rt2
</UL>

<P><STRONG><a name="[19e]"></a>__rt_entry_presh_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$00000002))

<P><STRONG><a name="[aa]"></a>__rt_entry_sh</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry4.o(.ARM.Collect$$rtentry$$00000004))
<BR><BR>[Stack]<UL><LI>Max Depth = 8 + Unknown Stack Size
<LI>Call Chain = __rt_entry_sh &rArr; __user_setup_stackheap
</UL>
<BR>[Calls]<UL><LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_setup_stackheap
</UL>

<P><STRONG><a name="[ac]"></a>__rt_entry_li</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$0000000A))
<BR><BR>[Calls]<UL><LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_init
</UL>

<P><STRONG><a name="[19f]"></a>__rt_entry_postsh_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$00000009))

<P><STRONG><a name="[ae]"></a>__rt_entry_main</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$0000000D))
<BR><BR>[Stack]<UL><LI>Max Depth = 728 + Unknown Stack Size
<LI>Call Chain = __rt_entry_main &rArr; main &rArr; Step_Motor_Set_Speed_my &rArr; Emm_V5_Vel_Control &rArr; my_printf &rArr; vsnprintf &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exit
</UL>

<P><STRONG><a name="[1a0]"></a>__rt_entry_postli_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$0000000C))

<P><STRONG><a name="[e0]"></a>__rt_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit.o(.ARM.Collect$$rtexit$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exit
</UL>

<P><STRONG><a name="[b1]"></a>__rt_exit_ls</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit2.o(.ARM.Collect$$rtexit$$00000003))
<BR><BR>[Calls]<UL><LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_shutdown
</UL>

<P><STRONG><a name="[1a1]"></a>__rt_exit_prels_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit2.o(.ARM.Collect$$rtexit$$00000002))

<P><STRONG><a name="[b3]"></a>__rt_exit_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit2.o(.ARM.Collect$$rtexit$$00000004))
<BR><BR>[Calls]<UL><LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_sys_exit
</UL>

<P><STRONG><a name="[b]"></a>Reset_Handler</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[27]"></a>ADC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[27]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_IRQHandler
</UL>
<BR>[Called By]<UL><LI><a href="#[27]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[29]"></a>CAN1_RX0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[2a]"></a>CAN1_RX1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[2b]"></a>CAN1_SCE_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[28]"></a>CAN1_TX_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[55]"></a>CAN2_RX0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[56]"></a>CAN2_RX1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[57]"></a>CAN2_SCE_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[54]"></a>CAN2_TX_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[63]"></a>DCMI_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[20]"></a>DMA1_Stream0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[21]"></a>DMA1_Stream1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[23]"></a>DMA1_Stream3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[24]"></a>DMA1_Stream4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[26]"></a>DMA1_Stream6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[44]"></a>DMA1_Stream7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[4d]"></a>DMA2_Stream0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[4f]"></a>DMA2_Stream2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[50]"></a>DMA2_Stream3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[51]"></a>DMA2_Stream4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[59]"></a>DMA2_Stream5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[5a]"></a>DMA2_Stream6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[5b]"></a>DMA2_Stream7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[52]"></a>ETH_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[53]"></a>ETH_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[1b]"></a>EXTI0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[3d]"></a>EXTI15_10_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[1c]"></a>EXTI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[1d]"></a>EXTI2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[1e]"></a>EXTI3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[1f]"></a>EXTI4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[2c]"></a>EXTI9_5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[19]"></a>FLASH_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[45]"></a>FMC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[65]"></a>FPU_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[64]"></a>HASH_RNG_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[35]"></a>I2C1_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[34]"></a>I2C1_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[37]"></a>I2C2_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[36]"></a>I2C2_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[5e]"></a>I2C3_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[5d]"></a>I2C3_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[58]"></a>OTG_FS_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[3f]"></a>OTG_FS_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[60]"></a>OTG_HS_EP1_IN_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[5f]"></a>OTG_HS_EP1_OUT_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[62]"></a>OTG_HS_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[61]"></a>OTG_HS_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[16]"></a>PVD_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[1a]"></a>RCC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[3e]"></a>RTC_Alarm_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[18]"></a>RTC_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[46]"></a>SDIO_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[38]"></a>SPI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[39]"></a>SPI2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[48]"></a>SPI3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[17]"></a>TAMP_STAMP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[2d]"></a>TIM1_BRK_TIM9_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[30]"></a>TIM1_CC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[2f]"></a>TIM1_TRG_COM_TIM11_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[2e]"></a>TIM1_UP_TIM10_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[31]"></a>TIM2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[32]"></a>TIM3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[33]"></a>TIM4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[47]"></a>TIM5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[4b]"></a>TIM6_DAC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[4c]"></a>TIM7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[40]"></a>TIM8_BRK_TIM12_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[43]"></a>TIM8_CC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[42]"></a>TIM8_TRG_COM_TIM14_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[41]"></a>TIM8_UP_TIM13_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[4a]"></a>UART5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[15]"></a>WWDG_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[df]"></a>__user_initial_stackheap</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, startup_stm32f407xx.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_setup_stackheap
</UL>

<P><STRONG><a name="[111]"></a>__aeabi_uldivmod</STRONG> (Thumb, 0 bytes, Stack size 48 bytes, lludivv7m.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = __aeabi_uldivmod
</UL>
<BR>[Called By]<UL><LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetSysClockFreq
<LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_SetConfig
</UL>

<P><STRONG><a name="[1a2]"></a>_ll_udiv</STRONG> (Thumb, 238 bytes, Stack size 48 bytes, lludivv7m.o(.text), UNUSED)

<P><STRONG><a name="[b5]"></a>vsnprintf</STRONG> (Thumb, 48 bytes, Stack size 24 bytes, vsnprintf.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 128 + Unknown Stack Size
<LI>Call Chain = vsnprintf &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_sputc
<LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_char_common
</UL>
<BR>[Called By]<UL><LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
</UL>

<P><STRONG><a name="[b8]"></a>_printf_str</STRONG> (Thumb, 82 bytes, Stack size 16 bytes, _printf_str.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = _printf_str &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_post_padding
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_pre_padding
</UL>
<BR>[Called By]<UL><LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_cs_common
</UL>

<P><STRONG><a name="[bb]"></a>__printf</STRONG> (Thumb, 388 bytes, Stack size 40 bytes, __printf_flags_ss_wp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40 + Unknown Stack Size
<LI>Call Chain = __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_percent
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_is_digit
</UL>
<BR>[Called By]<UL><LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_char_common
</UL>

<P><STRONG><a name="[be]"></a>__0sscanf</STRONG> (Thumb, 52 bytes, Stack size 72 bytes, __0sscanf.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 224<LI>Call Chain = __0sscanf &rArr; __vfscanf_char &rArr; __vfscanf &rArr; _scanf_int
</UL>
<BR>[Calls]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__vfscanf_char
</UL>
<BR>[Called By]<UL><LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pi_parse_data
</UL>

<P><STRONG><a name="[c0]"></a>_scanf_int</STRONG> (Thumb, 332 bytes, Stack size 56 bytes, _scanf_int.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = _scanf_int
</UL>
<BR>[Calls]<UL><LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_chval
</UL>
<BR>[Called By]<UL><LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__vfscanf
</UL>

<P><STRONG><a name="[c2]"></a>abort</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, abort.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 48 + Unknown Stack Size
<LI>Call Chain = abort &rArr; __rt_SIGABRT &rArr; __rt_SIGABRT_inner &rArr; __default_signal_display &rArr; _ttywrch
</UL>
<BR>[Calls]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_SIGABRT
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_sys_exit
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_shutdown
</UL>
<BR>[Called By]<UL><LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_ringbuffer_put
<LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_ringbuffer_init
<LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_ringbuffer_get
</UL>

<P><STRONG><a name="[172]"></a>strncmp</STRONG> (Thumb, 150 bytes, Stack size 16 bytes, strncmp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = strncmp
</UL>
<BR>[Called By]<UL><LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pi_parse_data
</UL>

<P><STRONG><a name="[177]"></a>__aeabi_memcpy</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, rt_memcpy_v6.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_ringbuffer_put
<LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_ringbuffer_get
</UL>

<P><STRONG><a name="[c4]"></a>__rt_memcpy</STRONG> (Thumb, 138 bytes, Stack size 0 bytes, rt_memcpy_v6.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>

<P><STRONG><a name="[1a3]"></a>_memcpy_lastbytes</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rt_memcpy_v6.o(.text), UNUSED)

<P><STRONG><a name="[eb]"></a>__aeabi_memclr4</STRONG> (Thumb, 0 bytes, Stack size 4 bytes, rt_memclr_w.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = __aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
<LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_RxEventCallback
<LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_MspPostInit
<LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Encoder_MspInit
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_MspInit
<LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM4_Init
<LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM3_Init
<LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM1_Init
<LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_GPIO_Init
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Emm_V5_Parse_Response
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_proc
</UL>

<P><STRONG><a name="[1a4]"></a>__aeabi_memclr8</STRONG> (Thumb, 0 bytes, Stack size 4 bytes, rt_memclr_w.o(.text), UNUSED)

<P><STRONG><a name="[1a5]"></a>__rt_memclr_w</STRONG> (Thumb, 78 bytes, Stack size 4 bytes, rt_memclr_w.o(.text), UNUSED)

<P><STRONG><a name="[1a6]"></a>_memset_w</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rt_memclr_w.o(.text), UNUSED)

<P><STRONG><a name="[1a7]"></a>__use_two_region_memory</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, heapauxi.o(.text), UNUSED)

<P><STRONG><a name="[8]"></a>__rt_heap_escrow</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, heapauxi.o(.text), UNUSED)

<P><STRONG><a name="[7]"></a>__rt_heap_expand</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, heapauxi.o(.text), UNUSED)

<P><STRONG><a name="[b4]"></a>_sys_exit</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, sys_exit.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__sig_exit
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;abort
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_exit_exit
</UL>

<P><STRONG><a name="[b9]"></a>_printf_pre_padding</STRONG> (Thumb, 44 bytes, Stack size 16 bytes, _printf_pad.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = _printf_pre_padding
</UL>
<BR>[Called By]<UL><LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_hex_real
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_dec_real
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_common
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_wctomb
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_str
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_infnan
</UL>

<P><STRONG><a name="[ba]"></a>_printf_post_padding</STRONG> (Thumb, 34 bytes, Stack size 16 bytes, _printf_pad.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = _printf_post_padding
</UL>
<BR>[Called By]<UL><LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_hex_real
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_dec_real
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_common
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_wctomb
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_str
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_infnan
</UL>

<P><STRONG><a name="[c6]"></a>_printf_truncate_signed</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, _printf_truncate.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_dec
</UL>

<P><STRONG><a name="[c7]"></a>_printf_truncate_unsigned</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, _printf_truncate.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_hex
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_oct
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_dec
</UL>

<P><STRONG><a name="[8c]"></a>_printf_int_dec</STRONG> (Thumb, 104 bytes, Stack size 24 bytes, _printf_dec.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = _printf_int_dec &rArr; _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_common
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_truncate_unsigned
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_truncate_signed
</UL>
<BR>[Called By]<UL><LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_u
<LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_d
<LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_i
</UL>

<P><STRONG><a name="[82]"></a>_printf_charcount</STRONG> (Thumb, 40 bytes, Stack size 0 bytes, _printf_charcount.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_n
</UL>

<P><STRONG><a name="[b6]"></a>_printf_char_common</STRONG> (Thumb, 32 bytes, Stack size 64 bytes, _printf_char_common.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 104 + Unknown Stack Size
<LI>Call Chain = _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__printf
</UL>
<BR>[Called By]<UL><LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vsnprintf
</UL>

<P><STRONG><a name="[b7]"></a>_sputc</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, _sputc.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vsnprintf
</UL>

<P><STRONG><a name="[68]"></a>_snputc</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, _snputc.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> vsnprintf.o(.text)
</UL>
<P><STRONG><a name="[c9]"></a>_printf_cs_common</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, _printf_char.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = _printf_cs_common &rArr; _printf_str &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_str
</UL>
<BR>[Called By]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_string
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_char
</UL>

<P><STRONG><a name="[9c]"></a>_printf_char</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, _printf_char.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = _printf_char &rArr; _printf_cs_common &rArr; _printf_str &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_cs_common
</UL>
<BR>[Called By]<UL><LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_c
</UL>

<P><STRONG><a name="[9e]"></a>_printf_string</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, _printf_char.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = _printf_string &rArr; _printf_cs_common &rArr; _printf_str &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_cs_common
</UL>
<BR>[Called By]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_s
</UL>

<P><STRONG><a name="[ca]"></a>_printf_wctomb</STRONG> (Thumb, 182 bytes, Stack size 56 bytes, _printf_wctomb.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = _printf_wctomb &rArr; _wcrtomb &rArr; __rt_ctype_table
</UL>
<BR>[Calls]<UL><LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_wcrtomb
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_post_padding
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_pre_padding
</UL>
<BR>[Called By]<UL><LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_lcs_common
</UL>

<P><STRONG><a name="[94]"></a>_printf_longlong_dec</STRONG> (Thumb, 108 bytes, Stack size 24 bytes, _printf_longlong_dec.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = _printf_longlong_dec &rArr; _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_common
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_ll_udiv10
</UL>
<BR>[Called By]<UL><LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_llu
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_lld
<LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_lli
</UL>

<P><STRONG><a name="[cd]"></a>_printf_longlong_oct</STRONG> (Thumb, 66 bytes, Stack size 8 bytes, _printf_oct_int_ll.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = _printf_longlong_oct &rArr; _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_common
</UL>
<BR>[Called By]<UL><LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_ll_oct
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_oct
</UL>

<P><STRONG><a name="[90]"></a>_printf_int_oct</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, _printf_oct_int_ll.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = _printf_int_oct &rArr; _printf_longlong_oct &rArr; _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_longlong_oct
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_truncate_unsigned
</UL>
<BR>[Called By]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_o
</UL>

<P><STRONG><a name="[98]"></a>_printf_ll_oct</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, _printf_oct_int_ll.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = _printf_ll_oct &rArr; _printf_longlong_oct &rArr; _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_longlong_oct
</UL>
<BR>[Called By]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_llo
</UL>

<P><STRONG><a name="[ce]"></a>_printf_longlong_hex</STRONG> (Thumb, 86 bytes, Stack size 16 bytes, _printf_hex_int_ll_ptr.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = _printf_longlong_hex &rArr; _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_common
</UL>
<BR>[Called By]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_hex_ptr
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_ll_hex
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_hex
</UL>

<P><STRONG><a name="[92]"></a>_printf_int_hex</STRONG> (Thumb, 28 bytes, Stack size 16 bytes, _printf_hex_int_ll_ptr.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = _printf_int_hex &rArr; _printf_longlong_hex &rArr; _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_longlong_hex
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_truncate_unsigned
</UL>
<BR>[Called By]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_x
</UL>

<P><STRONG><a name="[9a]"></a>_printf_ll_hex</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, _printf_hex_int_ll_ptr.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = _printf_ll_hex &rArr; _printf_longlong_hex &rArr; _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_longlong_hex
</UL>
<BR>[Called By]<UL><LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_llx
</UL>

<P><STRONG><a name="[84]"></a>_printf_hex_ptr</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, _printf_hex_int_ll_ptr.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = _printf_hex_ptr &rArr; _printf_longlong_hex &rArr; _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_longlong_hex
</UL>
<BR>[Called By]<UL><LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_p
</UL>

<P><STRONG><a name="[c1]"></a>_chval</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, _chval.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_scanf_int
</UL>

<P><STRONG><a name="[bf]"></a>__vfscanf_char</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, scanf_char.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = __vfscanf_char &rArr; __vfscanf &rArr; _scanf_int
</UL>
<BR>[Calls]<UL><LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__vfscanf
</UL>
<BR>[Called By]<UL><LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0sscanf
</UL>

<P><STRONG><a name="[69]"></a>_sgetc</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, _sgetc.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> __0sscanf.o(.text)
</UL>
<P><STRONG><a name="[6a]"></a>_sbackspace</STRONG> (Thumb, 34 bytes, Stack size 0 bytes, _sgetc.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> __0sscanf.o(.text)
</UL>
<P><STRONG><a name="[c3]"></a>__rt_SIGABRT</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, defsig_abrt_outer.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = __rt_SIGABRT &rArr; __rt_SIGABRT_inner &rArr; __default_signal_display &rArr; _ttywrch
</UL>
<BR>[Calls]<UL><LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_SIGABRT_inner
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__sig_exit
</UL>
<BR>[Called By]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;abort
</UL>

<P><STRONG><a name="[c5]"></a>__aeabi_memcpy4</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, rt_memcpy_w.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_memcpy
</UL>

<P><STRONG><a name="[1a8]"></a>__aeabi_memcpy8</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, rt_memcpy_w.o(.text), UNUSED)

<P><STRONG><a name="[1a9]"></a>__rt_memcpy_w</STRONG> (Thumb, 100 bytes, Stack size 8 bytes, rt_memcpy_w.o(.text), UNUSED)

<P><STRONG><a name="[1aa]"></a>_memcpy_lastbytes_aligned</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rt_memcpy_w.o(.text), UNUSED)

<P><STRONG><a name="[1ab]"></a>__I$use$semihosting</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, use_no_semi.o(.text), UNUSED)

<P><STRONG><a name="[1ac]"></a>__use_no_semihosting_swi</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, use_no_semi.o(.text), UNUSED)

<P><STRONG><a name="[1ad]"></a>__semihosting_library_function</STRONG> (Thumb, 0 bytes, Stack size 12 bytes, indicate_semi.o(.text), UNUSED)

<P><STRONG><a name="[cc]"></a>_ll_udiv10</STRONG> (Thumb, 138 bytes, Stack size 12 bytes, lludiv10.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = _ll_udiv10
</UL>
<BR>[Called By]<UL><LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_longlong_dec
</UL>

<P><STRONG><a name="[6d]"></a>isspace</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, isspace.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = isspace &rArr; __rt_ctype_table
</UL>
<BR>[Calls]<UL><LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_ctype_table
</UL>
<BR>[Address Reference Count : 1]<UL><LI> scanf_char.o(.text)
</UL>
<P><STRONG><a name="[c8]"></a>_printf_int_common</STRONG> (Thumb, 178 bytes, Stack size 32 bytes, _printf_intcommon.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_post_padding
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_pre_padding
</UL>
<BR>[Called By]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_longlong_hex
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_longlong_oct
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_longlong_dec
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_dec
</UL>

<P><STRONG><a name="[1ae]"></a>__lib_sel_fp_printf</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, _printf_fp_dec.o(.text), UNUSED)

<P><STRONG><a name="[d8]"></a>_printf_fp_dec_real</STRONG> (Thumb, 620 bytes, Stack size 104 bytes, _printf_fp_dec.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 324<LI>Call Chain = _printf_fp_dec_real &rArr; _fp_digits &rArr; _btod_etento &rArr; _btod_emul &rArr; _e2e
</UL>
<BR>[Calls]<UL><LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_post_padding
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_pre_padding
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_fpclassify
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_infnan
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_locale
</UL>
<BR>[Called By]<UL><LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_dec
</UL>

<P><STRONG><a name="[db]"></a>_printf_fp_hex_real</STRONG> (Thumb, 756 bytes, Stack size 72 bytes, _printf_fp_hex.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = _printf_fp_hex_real &rArr; _printf_fp_infnan &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_post_padding
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_pre_padding
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_fpclassify
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_infnan
</UL>
<BR>[Called By]<UL><LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_hex
</UL>

<P><STRONG><a name="[dc]"></a>_printf_lcs_common</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, _printf_wchar.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = _printf_lcs_common &rArr; _printf_wctomb &rArr; _wcrtomb &rArr; __rt_ctype_table
</UL>
<BR>[Calls]<UL><LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_wctomb
</UL>
<BR>[Called By]<UL><LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_wstring
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_wchar
</UL>

<P><STRONG><a name="[a0]"></a>_printf_wchar</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, _printf_wchar.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = _printf_wchar &rArr; _printf_lcs_common &rArr; _printf_wctomb &rArr; _wcrtomb &rArr; __rt_ctype_table
</UL>
<BR>[Calls]<UL><LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_lcs_common
</UL>
<BR>[Called By]<UL><LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_lc
</UL>

<P><STRONG><a name="[a2]"></a>_printf_wstring</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, _printf_wchar.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = _printf_wstring &rArr; _printf_lcs_common &rArr; _printf_wctomb &rArr; _wcrtomb &rArr; __rt_ctype_table
</UL>
<BR>[Calls]<UL><LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_lcs_common
</UL>
<BR>[Called By]<UL><LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_ls
</UL>

<P><STRONG><a name="[cf]"></a>__vfscanf</STRONG> (Thumb, 880 bytes, Stack size 96 bytes, _scanf.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = __vfscanf &rArr; _scanf_int
</UL>
<BR>[Calls]<UL><LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_scanf_int
</UL>
<BR>[Called By]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__vfscanf_char
</UL>

<P><STRONG><a name="[cb]"></a>_wcrtomb</STRONG> (Thumb, 64 bytes, Stack size 16 bytes, _wcrtomb.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = _wcrtomb &rArr; __rt_ctype_table
</UL>
<BR>[Calls]<UL><LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_ctype_table
</UL>
<BR>[Called By]<UL><LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_wctomb
</UL>

<P><STRONG><a name="[d1]"></a>__sig_exit</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, defsig_exit.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_sys_exit
</UL>
<BR>[Called By]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_SIGABRT
</UL>

<P><STRONG><a name="[d0]"></a>__rt_SIGABRT_inner</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, defsig_abrt_inner.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = __rt_SIGABRT_inner &rArr; __default_signal_display &rArr; _ttywrch
</UL>
<BR>[Calls]<UL><LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__default_signal_display
</UL>
<BR>[Called By]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_SIGABRT
</UL>

<P><STRONG><a name="[1af]"></a>__user_libspace</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, libspace.o(.text), UNUSED)

<P><STRONG><a name="[de]"></a>__user_perproc_libspace</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, libspace.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_setup_stackheap
</UL>

<P><STRONG><a name="[1b0]"></a>__user_perthread_libspace</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, libspace.o(.text), UNUSED)

<P><STRONG><a name="[ab]"></a>__user_setup_stackheap</STRONG> (Thumb, 74 bytes, Stack size 8 bytes, sys_stackheap_outer.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8 + Unknown Stack Size
<LI>Call Chain = __user_setup_stackheap
</UL>
<BR>[Calls]<UL><LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_initial_stackheap
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_perproc_libspace
</UL>
<BR>[Called By]<UL><LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_sh
</UL>

<P><STRONG><a name="[d2]"></a>__rt_ctype_table</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, rt_ctype_table.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __rt_ctype_table
</UL>
<BR>[Calls]<UL><LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_locale
</UL>
<BR>[Called By]<UL><LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_wcrtomb
<LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;isspace
</UL>

<P><STRONG><a name="[a6]"></a>__rt_locale</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, rt_locale_intlibspace.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_dec_real
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_init_lc_common
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_ctype_table
</UL>

<P><STRONG><a name="[da]"></a>_printf_fp_infnan</STRONG> (Thumb, 112 bytes, Stack size 24 bytes, _printf_fp_infnan.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = _printf_fp_infnan &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_post_padding
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_pre_padding
</UL>
<BR>[Called By]<UL><LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_hex_real
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_dec_real
</UL>

<P><STRONG><a name="[d4]"></a>_btod_etento</STRONG> (Thumb, 224 bytes, Stack size 72 bytes, bigflt0.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 124<LI>Call Chain = _btod_etento &rArr; _btod_emul &rArr; _e2e
</UL>
<BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_emul
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_ediv
</UL>
<BR>[Called By]<UL><LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[b0]"></a>exit</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, exit.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8 + Unknown Stack Size
<LI>Call Chain = exit
</UL>
<BR>[Calls]<UL><LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_exit
</UL>
<BR>[Called By]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_main
</UL>

<P><STRONG><a name="[dd]"></a>__default_signal_display</STRONG> (Thumb, 50 bytes, Stack size 16 bytes, defsig_general.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = __default_signal_display &rArr; _ttywrch
</UL>
<BR>[Calls]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_ttywrch
</UL>
<BR>[Called By]<UL><LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_SIGABRT_inner
</UL>

<P><STRONG><a name="[e1]"></a>_ttywrch</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, sys_wrch.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = _ttywrch
</UL>
<BR>[Called By]<UL><LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__default_signal_display
</UL>

<P><STRONG><a name="[178]"></a>strcmp</STRONG> (Thumb, 128 bytes, Stack size 0 bytes, strcmpv7m.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_get_lc_ctype
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_get_lc_numeric
</UL>

<P><STRONG><a name="[d5]"></a>_btod_d2e</STRONG> (Thumb, 62 bytes, Stack size 0 bytes, btod.o(CL$$btod_d2e))
<BR><BR>[Calls]<UL><LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_d2e_norm_op1
</UL>
<BR>[Called By]<UL><LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[e3]"></a>_d2e_denorm_low</STRONG> (Thumb, 70 bytes, Stack size 0 bytes, btod.o(CL$$btod_d2e_denorm_low))
<BR><BR>[Called By]<UL><LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_d2e_norm_op1
</UL>

<P><STRONG><a name="[e2]"></a>_d2e_norm_op1</STRONG> (Thumb, 96 bytes, Stack size 0 bytes, btod.o(CL$$btod_d2e_norm_op1))
<BR><BR>[Calls]<UL><LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_d2e_denorm_low
</UL>
<BR>[Called By]<UL><LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_d2e
</UL>

<P><STRONG><a name="[e4]"></a>__btod_div_common</STRONG> (Thumb, 696 bytes, Stack size 24 bytes, btod.o(CL$$btod_div_common))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = __btod_div_common
</UL>
<BR>[Called By]<UL><LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_ediv
</UL>

<P><STRONG><a name="[e5]"></a>_e2e</STRONG> (Thumb, 220 bytes, Stack size 24 bytes, btod.o(CL$$btod_e2e))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = _e2e
</UL>
<BR>[Called By]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_emul
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_ediv
</UL>

<P><STRONG><a name="[d6]"></a>_btod_ediv</STRONG> (Thumb, 42 bytes, Stack size 28 bytes, btod.o(CL$$btod_ediv))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = _btod_ediv &rArr; _e2e
</UL>
<BR>[Calls]<UL><LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_e2e
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__btod_div_common
</UL>
<BR>[Called By]<UL><LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_etento
</UL>

<P><STRONG><a name="[d7]"></a>_btod_emul</STRONG> (Thumb, 42 bytes, Stack size 28 bytes, btod.o(CL$$btod_emul))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = _btod_emul &rArr; _e2e
</UL>
<BR>[Calls]<UL><LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__btod_mult_common
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_e2e
</UL>
<BR>[Called By]<UL><LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_etento
</UL>

<P><STRONG><a name="[e6]"></a>__btod_mult_common</STRONG> (Thumb, 580 bytes, Stack size 16 bytes, btod.o(CL$$btod_mult_common))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __btod_mult_common
</UL>
<BR>[Called By]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_emul
</UL>

<P><STRONG><a name="[f]"></a>BusFault_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.BusFault_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BusFault_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BusFault_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[22]"></a>DMA1_Stream2_IRQHandler</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.DMA1_Stream2_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = DMA1_Stream2_IRQHandler &rArr; HAL_DMA_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[25]"></a>DMA1_Stream5_IRQHandler</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.DMA1_Stream5_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = DMA1_Stream5_IRQHandler &rArr; HAL_DMA_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[4e]"></a>DMA2_Stream1_IRQHandler</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.DMA2_Stream1_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = DMA2_Stream1_IRQHandler &rArr; HAL_DMA_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[12]"></a>DebugMon_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.DebugMon_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[e8]"></a>Emm_V5_En_Control</STRONG> (Thumb, 56 bytes, Stack size 24 bytes, emm_v5.o(i.Emm_V5_En_Control))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = Emm_V5_En_Control &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Transmit
</UL>
<BR>[Called By]<UL><LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Step_Motor_Init
</UL>

<P><STRONG><a name="[ea]"></a>Emm_V5_Parse_Response</STRONG> (Thumb, 492 bytes, Stack size 16 bytes, emm_v5.o(i.Emm_V5_Parse_Response))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = Emm_V5_Parse_Response &rArr; __aeabi_memclr4
</UL>
<BR>[Calls]<UL><LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_proc
</UL>

<P><STRONG><a name="[ec]"></a>Emm_V5_Pos_Control</STRONG> (Thumb, 112 bytes, Stack size 40 bytes, emm_v5.o(i.Emm_V5_Pos_Control))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = Emm_V5_Pos_Control &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Transmit
</UL>
<BR>[Called By]<UL><LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Step_Motor_Set_Pwm
</UL>

<P><STRONG><a name="[ed]"></a>Emm_V5_Read_Sys_Params</STRONG> (Thumb, 142 bytes, Stack size 24 bytes, emm_v5.o(i.Emm_V5_Read_Sys_Params))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = Emm_V5_Read_Sys_Params &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Transmit
</UL>
<BR>[Called By]<UL><LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;save_initial_position
</UL>

<P><STRONG><a name="[ee]"></a>Emm_V5_Reset_CurPos_To_Zero</STRONG> (Thumb, 48 bytes, Stack size 24 bytes, emm_v5.o(i.Emm_V5_Reset_CurPos_To_Zero))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = Emm_V5_Reset_CurPos_To_Zero &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Transmit
</UL>
<BR>[Called By]<UL><LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[ef]"></a>Emm_V5_Stop_Now</STRONG> (Thumb, 52 bytes, Stack size 24 bytes, emm_v5.o(i.Emm_V5_Stop_Now))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = Emm_V5_Stop_Now &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Transmit
</UL>
<BR>[Called By]<UL><LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Step_Motor_Stop
<LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;check_motor_angle_limits
</UL>

<P><STRONG><a name="[f0]"></a>Emm_V5_Vel_Control</STRONG> (Thumb, 122 bytes, Stack size 32 bytes, emm_v5.o(i.Emm_V5_Vel_Control))
<BR><BR>[Stack]<UL><LI>Max Depth = 704 + Unknown Stack Size
<LI>Call Chain = Emm_V5_Vel_Control &rArr; my_printf &rArr; vsnprintf &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Transmit
</UL>
<BR>[Called By]<UL><LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Step_Motor_Set_Speed_my
</UL>

<P><STRONG><a name="[f2]"></a>Encoder_Driver_Init</STRONG> (Thumb, 38 bytes, Stack size 8 bytes, encoder_drv.o(i.Encoder_Driver_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = Encoder_Driver_Init &rArr; HAL_TIM_Encoder_Start &rArr; TIM_CCxChannelCmd
</UL>
<BR>[Calls]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Encoder_Start
</UL>
<BR>[Called By]<UL><LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;encoder_init
</UL>

<P><STRONG><a name="[164]"></a>Encoder_Driver_Update</STRONG> (Thumb, 84 bytes, Stack size 0 bytes, encoder_drv.o(i.Encoder_Driver_Update))
<BR><BR>[Called By]<UL><LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;encoder_proc
</UL>

<P><STRONG><a name="[134]"></a>Error_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, main.o(i.Error_Handler))
<BR><BR>[Called By]<UL><LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
<LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART6_UART_Init
<LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART3_UART_Init
<LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART2_UART_Init
<LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART1_UART_Init
<LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_UART5_Init
<LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_UART4_Init
<LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM4_Init
<LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM3_Init
<LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM1_Init
<LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_I2C2_Init
<LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_I2C1_Init
</UL>

<P><STRONG><a name="[f4]"></a>HAL_DMA_Abort</STRONG> (Thumb, 146 bytes, Stack size 24 bytes, stm32f4xx_hal_dma.o(i.HAL_DMA_Abort))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = HAL_DMA_Abort
</UL>
<BR>[Calls]<UL><LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
</UL>

<P><STRONG><a name="[12e]"></a>HAL_DMA_Abort_IT</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT))
<BR><BR>[Called By]<UL><LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
</UL>

<P><STRONG><a name="[e7]"></a>HAL_DMA_IRQHandler</STRONG> (Thumb, 412 bytes, Stack size 32 bytes, stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = HAL_DMA_IRQHandler
</UL>
<BR>[Called By]<UL><LI><a href="#[4e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA2_Stream1_IRQHandler
<LI><a href="#[25]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA1_Stream5_IRQHandler
<LI><a href="#[22]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA1_Stream2_IRQHandler
</UL>

<P><STRONG><a name="[f6]"></a>HAL_DMA_Init</STRONG> (Thumb, 206 bytes, Stack size 24 bytes, stm32f4xx_hal_dma.o(i.HAL_DMA_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = HAL_DMA_Init &rArr; DMA_CheckFifoParam
</UL>
<BR>[Calls]<UL><LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_CheckFifoParam
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_CalcBaseAndBitshift
</UL>
<BR>[Called By]<UL><LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
</UL>

<P><STRONG><a name="[f9]"></a>HAL_DMA_Start_IT</STRONG> (Thumb, 110 bytes, Stack size 24 bytes, stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = HAL_DMA_Start_IT &rArr; DMA_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_Start_Receive_DMA
</UL>

<P><STRONG><a name="[fb]"></a>HAL_Delay</STRONG> (Thumb, 32 bytes, Stack size 16 bytes, stm32f4xx_hal.o(i.HAL_Delay))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = HAL_Delay
</UL>
<BR>[Calls]<UL><LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
</UL>

<P><STRONG><a name="[107]"></a>HAL_GPIO_Init</STRONG> (Thumb, 450 bytes, Stack size 40 bytes, stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = HAL_GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
<LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_MspPostInit
<LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Encoder_MspInit
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_MspInit
<LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_GPIO_Init
</UL>

<P><STRONG><a name="[166]"></a>HAL_GPIO_ReadPin</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, stm32f4xx_hal_gpio.o(i.HAL_GPIO_ReadPin))
<BR><BR>[Called By]<UL><LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;key_read
</UL>

<P><STRONG><a name="[140]"></a>HAL_GPIO_WritePin</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin))
<BR><BR>[Called By]<UL><LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_GPIO_Init
<LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Motor_SetSpeed
<LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Motor_Create
</UL>

<P><STRONG><a name="[f5]"></a>HAL_GetTick</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f4xx_hal.o(i.HAL_GetTick))
<BR><BR>[Called By]<UL><LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Init
<LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;schedule_run
<LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_OscConfig
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_ClockConfig
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Mem_Write
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Mem_Read
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnTXEFlagUntilTimeout
<LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnRXNEFlagUntilTimeout
<LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnMasterAddressFlagUntilTimeout
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnFlagUntilTimeout
<LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnBTFFlagUntilTimeout
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Abort
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Transmit
<LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_WaitOnFlagUntilTimeout
</UL>

<P><STRONG><a name="[fc]"></a>HAL_I2C_Init</STRONG> (Thumb, 376 bytes, Stack size 16 bytes, stm32f4xx_hal_i2c.o(i.HAL_I2C_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = HAL_I2C_Init &rArr; HAL_I2C_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_MspInit
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetPCLK1Freq
</UL>
<BR>[Called By]<UL><LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_I2C2_Init
<LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_I2C1_Init
</UL>

<P><STRONG><a name="[ff]"></a>HAL_I2C_Mem_Read</STRONG> (Thumb, 502 bytes, Stack size 64 bytes, stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = HAL_I2C_Mem_Read &rArr; I2C_RequestMemoryRead &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnRXNEFlagUntilTimeout
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnFlagUntilTimeout
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_RequestMemoryRead
</UL>
<BR>[Called By]<UL><LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_ReadBytes
</UL>

<P><STRONG><a name="[103]"></a>HAL_I2C_Mem_Write</STRONG> (Thumb, 294 bytes, Stack size 64 bytes, stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = HAL_I2C_Mem_Write &rArr; I2C_RequestMemoryWrite &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnTXEFlagUntilTimeout
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnFlagUntilTimeout
<LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnBTFFlagUntilTimeout
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_RequestMemoryWrite
</UL>
<BR>[Called By]<UL><LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Write_data
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Write_cmd
</UL>

<P><STRONG><a name="[fd]"></a>HAL_I2C_MspInit</STRONG> (Thumb, 154 bytes, Stack size 48 bytes, i2c.o(i.HAL_I2C_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = HAL_I2C_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Init
</UL>

<P><STRONG><a name="[15c]"></a>HAL_IncTick</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, stm32f4xx_hal.o(i.HAL_IncTick))
<BR><BR>[Called By]<UL><LI><a href="#[14]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Handler
</UL>

<P><STRONG><a name="[108]"></a>HAL_Init</STRONG> (Thumb, 48 bytes, Stack size 8 bytes, stm32f4xx_hal.o(i.HAL_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = HAL_Init &rArr; HAL_InitTick &rArr; HAL_NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_MspInit
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriorityGrouping
</UL>
<BR>[Called By]<UL><LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[10a]"></a>HAL_InitTick</STRONG> (Thumb, 54 bytes, Stack size 16 bytes, stm32f4xx_hal.o(i.HAL_InitTick))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = HAL_InitTick &rArr; HAL_NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
<LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SYSTICK_Config
</UL>
<BR>[Called By]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_ClockConfig
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Init
</UL>

<P><STRONG><a name="[10b]"></a>HAL_MspInit</STRONG> (Thumb, 42 bytes, Stack size 8 bytes, stm32f4xx_hal_msp.o(i.HAL_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = HAL_MspInit
</UL>
<BR>[Called By]<UL><LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Init
</UL>

<P><STRONG><a name="[135]"></a>HAL_NVIC_EnableIRQ</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ))
<BR><BR>[Called By]<UL><LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_DMA_Init
</UL>

<P><STRONG><a name="[10d]"></a>HAL_NVIC_SetPriority</STRONG> (Thumb, 60 bytes, Stack size 16 bytes, stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = HAL_NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__NVIC_SetPriority
</UL>
<BR>[Called By]<UL><LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_DMA_Init
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
</UL>

<P><STRONG><a name="[109]"></a>HAL_NVIC_SetPriorityGrouping</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping))
<BR><BR>[Called By]<UL><LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Init
</UL>

<P><STRONG><a name="[10f]"></a>HAL_RCC_ClockConfig</STRONG> (Thumb, 288 bytes, Stack size 32 bytes, stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = HAL_RCC_ClockConfig &rArr; HAL_RCC_GetSysClockFreq &rArr; __aeabi_uldivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetSysClockFreq
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
</UL>

<P><STRONG><a name="[fe]"></a>HAL_RCC_GetPCLK1Freq</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq))
<BR><BR>[Called By]<UL><LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Init
<LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_SetConfig
</UL>

<P><STRONG><a name="[161]"></a>HAL_RCC_GetPCLK2Freq</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq))
<BR><BR>[Called By]<UL><LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_SetConfig
</UL>

<P><STRONG><a name="[110]"></a>HAL_RCC_GetSysClockFreq</STRONG> (Thumb, 88 bytes, Stack size 8 bytes, stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = HAL_RCC_GetSysClockFreq &rArr; __aeabi_uldivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
</UL>
<BR>[Called By]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_ClockConfig
</UL>

<P><STRONG><a name="[112]"></a>HAL_RCC_OscConfig</STRONG> (Thumb, 856 bytes, Stack size 40 bytes, stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = HAL_RCC_OscConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
</UL>

<P><STRONG><a name="[10c]"></a>HAL_SYSTICK_Config</STRONG> (Thumb, 40 bytes, Stack size 8 bytes, stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = HAL_SYSTICK_Config
</UL>
<BR>[Calls]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__NVIC_SetPriority
</UL>
<BR>[Called By]<UL><LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
</UL>

<P><STRONG><a name="[145]"></a>HAL_TIMEx_ConfigBreakDeadTime</STRONG> (Thumb, 84 bytes, Stack size 8 bytes, stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigBreakDeadTime))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = HAL_TIMEx_ConfigBreakDeadTime
</UL>
<BR>[Called By]<UL><LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM1_Init
</UL>

<P><STRONG><a name="[144]"></a>HAL_TIMEx_MasterConfigSynchronization</STRONG> (Thumb, 116 bytes, Stack size 16 bytes, stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = HAL_TIMEx_MasterConfigSynchronization
</UL>
<BR>[Called By]<UL><LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM4_Init
<LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM3_Init
<LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM1_Init
</UL>

<P><STRONG><a name="[113]"></a>HAL_TIM_Base_Init</STRONG> (Thumb, 90 bytes, Stack size 8 bytes, stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = HAL_TIM_Base_Init &rArr; TIM_Base_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_MspInit
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_Base_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM1_Init
</UL>

<P><STRONG><a name="[114]"></a>HAL_TIM_Base_MspInit</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, tim.o(i.HAL_TIM_Base_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = HAL_TIM_Base_MspInit
</UL>
<BR>[Called By]<UL><LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_Init
</UL>

<P><STRONG><a name="[116]"></a>HAL_TIM_ConfigClockSource</STRONG> (Thumb, 220 bytes, Stack size 16 bytes, stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = HAL_TIM_ConfigClockSource &rArr; TIM_ETR_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ETR_SetConfig
<LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_TI2_ConfigInputStage
<LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_TI1_ConfigInputStage
<LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ITRx_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM1_Init
</UL>

<P><STRONG><a name="[11b]"></a>HAL_TIM_Encoder_Init</STRONG> (Thumb, 164 bytes, Stack size 24 bytes, stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = HAL_TIM_Encoder_Init &rArr; HAL_TIM_Encoder_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Encoder_MspInit
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_Base_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM4_Init
<LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM3_Init
</UL>

<P><STRONG><a name="[11c]"></a>HAL_TIM_Encoder_MspInit</STRONG> (Thumb, 142 bytes, Stack size 40 bytes, tim.o(i.HAL_TIM_Encoder_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = HAL_TIM_Encoder_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Encoder_Init
</UL>

<P><STRONG><a name="[f3]"></a>HAL_TIM_Encoder_Start</STRONG> (Thumb, 142 bytes, Stack size 16 bytes, stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = HAL_TIM_Encoder_Start &rArr; TIM_CCxChannelCmd
</UL>
<BR>[Calls]<UL><LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_CCxChannelCmd
</UL>
<BR>[Called By]<UL><LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Encoder_Driver_Init
</UL>

<P><STRONG><a name="[11e]"></a>HAL_TIM_MspPostInit</STRONG> (Thumb, 72 bytes, Stack size 32 bytes, tim.o(i.HAL_TIM_MspPostInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = HAL_TIM_MspPostInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM1_Init
</UL>

<P><STRONG><a name="[11f]"></a>HAL_TIM_PWM_ConfigChannel</STRONG> (Thumb, 204 bytes, Stack size 16 bytes, stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = HAL_TIM_PWM_ConfigChannel &rArr; TIM_OC2_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_OC2_SetConfig
<LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_OC4_SetConfig
<LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_OC3_SetConfig
<LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_OC1_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM1_Init
</UL>

<P><STRONG><a name="[124]"></a>HAL_TIM_PWM_Init</STRONG> (Thumb, 90 bytes, Stack size 8 bytes, stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = HAL_TIM_PWM_Init &rArr; TIM_Base_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_PWM_MspInit
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_Base_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM1_Init
</UL>

<P><STRONG><a name="[125]"></a>HAL_TIM_PWM_MspInit</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_MspInit))
<BR><BR>[Called By]<UL><LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_PWM_Init
</UL>

<P><STRONG><a name="[126]"></a>HAL_TIM_PWM_Start</STRONG> (Thumb, 172 bytes, Stack size 8 bytes, stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = HAL_TIM_PWM_Start &rArr; TIM_CCxChannelCmd
</UL>
<BR>[Calls]<UL><LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_CCxChannelCmd
</UL>
<BR>[Called By]<UL><LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Motor_Create
</UL>

<P><STRONG><a name="[127]"></a>HAL_UARTEx_ReceiveToIdle_DMA</STRONG> (Thumb, 74 bytes, Stack size 16 bytes, stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA))
<BR><BR>[Stack]<UL><LI>Max Depth = 68<LI>Call Chain = HAL_UARTEx_ReceiveToIdle_DMA &rArr; UART_Start_Receive_DMA &rArr; HAL_DMA_Start_IT &rArr; DMA_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_Start_Receive_DMA
</UL>
<BR>[Called By]<UL><LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART6_IRQHandler
<LI><a href="#[3b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART2_IRQHandler
<LI><a href="#[49]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART4_IRQHandler
<LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART6_UART_Init
<LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART2_UART_Init
<LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_UART4_Init
</UL>

<P><STRONG><a name="[129]"></a>HAL_UARTEx_RxEventCallback</STRONG> (Thumb, 98 bytes, Stack size 16 bytes, usart.o(i.HAL_UARTEx_RxEventCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 80 + Unknown Stack Size
<LI>Call Chain = HAL_UARTEx_RxEventCallback &rArr; rt_ringbuffer_put &rArr; abort &rArr; __rt_SIGABRT &rArr; __rt_SIGABRT_inner &rArr; __default_signal_display &rArr; _ttywrch
</UL>
<BR>[Calls]<UL><LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_ringbuffer_put
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
<LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_Receive_IT
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_DMARxHalfCplt
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_DMAReceiveCplt
</UL>

<P><STRONG><a name="[12f]"></a>HAL_UART_ErrorCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback))
<BR><BR>[Called By]<UL><LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_DMAError
<LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_DMAAbortOnError
</UL>

<P><STRONG><a name="[12b]"></a>HAL_UART_IRQHandler</STRONG> (Thumb, 636 bytes, Stack size 24 bytes, stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 112 + Unknown Stack Size
<LI>Call Chain = HAL_UART_IRQHandler &rArr; UART_Receive_IT &rArr; HAL_UARTEx_RxEventCallback &rArr; rt_ringbuffer_put &rArr; abort &rArr; __rt_SIGABRT &rArr; __rt_SIGABRT_inner &rArr; __default_signal_display &rArr; _ttywrch
</UL>
<BR>[Calls]<UL><LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_RxEventCallback
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Abort_IT
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Abort
<LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_TxCpltCallback
<LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_ErrorCallback
<LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_Receive_IT
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_EndRxTransfer
</UL>
<BR>[Called By]<UL><LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART6_IRQHandler
<LI><a href="#[3c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART3_IRQHandler
<LI><a href="#[3b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART2_IRQHandler
<LI><a href="#[3a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART1_IRQHandler
<LI><a href="#[49]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART4_IRQHandler
</UL>

<P><STRONG><a name="[131]"></a>HAL_UART_Init</STRONG> (Thumb, 100 bytes, Stack size 16 bytes, stm32f4xx_hal_uart.o(i.HAL_UART_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = HAL_UART_Init &rArr; HAL_UART_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
<LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART6_UART_Init
<LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART3_UART_Init
<LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART2_UART_Init
<LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART1_UART_Init
<LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_UART5_Init
<LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_UART4_Init
</UL>

<P><STRONG><a name="[132]"></a>HAL_UART_MspInit</STRONG> (Thumb, 674 bytes, Stack size 64 bytes, usart.o(i.HAL_UART_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = HAL_UART_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Init
<LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_EnableIRQ
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
<LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Init
</UL>

<P><STRONG><a name="[15f]"></a>HAL_UART_RxCpltCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_uart.o(i.HAL_UART_RxCpltCallback))
<BR><BR>[Called By]<UL><LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_Receive_IT
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_DMAReceiveCplt
</UL>

<P><STRONG><a name="[160]"></a>HAL_UART_RxHalfCpltCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback))
<BR><BR>[Called By]<UL><LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_DMARxHalfCplt
</UL>

<P><STRONG><a name="[e9]"></a>HAL_UART_Transmit</STRONG> (Thumb, 160 bytes, Stack size 32 bytes, stm32f4xx_hal_uart.o(i.HAL_UART_Transmit))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Called By]<UL><LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Emm_V5_Reset_CurPos_To_Zero
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Emm_V5_Vel_Control
<LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Emm_V5_En_Control
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Emm_V5_Stop_Now
<LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Emm_V5_Read_Sys_Params
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Emm_V5_Pos_Control
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
</UL>

<P><STRONG><a name="[130]"></a>HAL_UART_TxCpltCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback))
<BR><BR>[Called By]<UL><LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
</UL>

<P><STRONG><a name="[d]"></a>HardFault_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.HardFault_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HardFault_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HardFault_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[139]"></a>IIC_Get_Digtal</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, hardware_iic.o(i.IIC_Get_Digtal))
<BR><BR>[Stack]<UL><LI>Max Depth = 168<LI>Call Chain = IIC_Get_Digtal &rArr; IIC_ReadBytes &rArr; HAL_I2C_Mem_Read &rArr; I2C_RequestMemoryRead &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_ReadBytes
</UL>
<BR>[Called By]<UL><LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gray_proc
</UL>

<P><STRONG><a name="[13a]"></a>IIC_ReadBytes</STRONG> (Thumb, 32 bytes, Stack size 24 bytes, hardware_iic.o(i.IIC_ReadBytes))
<BR><BR>[Stack]<UL><LI>Max Depth = 160<LI>Call Chain = IIC_ReadBytes &rArr; HAL_I2C_Mem_Read &rArr; I2C_RequestMemoryRead &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Mem_Read
</UL>
<BR>[Called By]<UL><LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_Get_Digtal
</UL>

<P><STRONG><a name="[13b]"></a>Location_Speed_control</STRONG> (Thumb, 142 bytes, Stack size 16 bytes, motor_bsp.o(i.Location_Speed_control))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = Location_Speed_control &rArr; Motor_SetSpeed
</UL>
<BR>[Calls]<UL><LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pid_calc
<LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Motor_SetSpeed
</UL>
<BR>[Called By]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;motor_proc
</UL>

<P><STRONG><a name="[13e]"></a>MX_DMA_Init</STRONG> (Thumb, 88 bytes, Stack size 8 bytes, dma.o(i.MX_DMA_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = MX_DMA_Init &rArr; HAL_NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_EnableIRQ
</UL>
<BR>[Called By]<UL><LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[13f]"></a>MX_GPIO_Init</STRONG> (Thumb, 268 bytes, Stack size 64 bytes, gpio.o(i.MX_GPIO_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = MX_GPIO_Init &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[141]"></a>MX_I2C1_Init</STRONG> (Thumb, 50 bytes, Stack size 8 bytes, i2c.o(i.MX_I2C1_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = MX_I2C1_Init &rArr; HAL_I2C_Init &rArr; HAL_I2C_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Init
<LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[142]"></a>MX_I2C2_Init</STRONG> (Thumb, 50 bytes, Stack size 8 bytes, i2c.o(i.MX_I2C2_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = MX_I2C2_Init &rArr; HAL_I2C_Init &rArr; HAL_I2C_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Init
<LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[143]"></a>MX_TIM1_Init</STRONG> (Thumb, 206 bytes, Stack size 96 bytes, tim.o(i.MX_TIM1_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 168<LI>Call Chain = MX_TIM1_Init &rArr; HAL_TIM_MspPostInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_PWM_Init
<LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_PWM_ConfigChannel
<LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_ConfigClockSource
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_Init
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIMEx_MasterConfigSynchronization
<LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIMEx_ConfigBreakDeadTime
<LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_MspPostInit
<LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[146]"></a>MX_TIM3_Init</STRONG> (Thumb, 98 bytes, Stack size 56 bytes, tim.o(i.MX_TIM3_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 160<LI>Call Chain = MX_TIM3_Init &rArr; HAL_TIM_Encoder_Init &rArr; HAL_TIM_Encoder_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Encoder_Init
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIMEx_MasterConfigSynchronization
<LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[147]"></a>MX_TIM4_Init</STRONG> (Thumb, 98 bytes, Stack size 56 bytes, tim.o(i.MX_TIM4_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 160<LI>Call Chain = MX_TIM4_Init &rArr; HAL_TIM_Encoder_Init &rArr; HAL_TIM_Encoder_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Encoder_Init
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIMEx_MasterConfigSynchronization
<LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[148]"></a>MX_UART4_Init</STRONG> (Thumb, 66 bytes, Stack size 8 bytes, usart.o(i.MX_UART4_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = MX_UART4_Init &rArr; HAL_UART_Init &rArr; HAL_UART_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Init
<LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_ReceiveToIdle_DMA
<LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[149]"></a>MX_UART5_Init</STRONG> (Thumb, 48 bytes, Stack size 8 bytes, usart.o(i.MX_UART5_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = MX_UART5_Init &rArr; HAL_UART_Init &rArr; HAL_UART_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Init
<LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[14a]"></a>MX_USART1_UART_Init</STRONG> (Thumb, 48 bytes, Stack size 8 bytes, usart.o(i.MX_USART1_UART_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = MX_USART1_UART_Init &rArr; HAL_UART_Init &rArr; HAL_UART_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Init
<LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[14b]"></a>MX_USART2_UART_Init</STRONG> (Thumb, 64 bytes, Stack size 8 bytes, usart.o(i.MX_USART2_UART_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = MX_USART2_UART_Init &rArr; HAL_UART_Init &rArr; HAL_UART_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Init
<LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_ReceiveToIdle_DMA
<LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[14c]"></a>MX_USART3_UART_Init</STRONG> (Thumb, 48 bytes, Stack size 8 bytes, usart.o(i.MX_USART3_UART_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = MX_USART3_UART_Init &rArr; HAL_UART_Init &rArr; HAL_UART_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Init
<LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[14d]"></a>MX_USART6_UART_Init</STRONG> (Thumb, 64 bytes, Stack size 8 bytes, usart.o(i.MX_USART6_UART_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = MX_USART6_UART_Init &rArr; HAL_UART_Init &rArr; HAL_UART_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Init
<LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_ReceiveToIdle_DMA
<LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[e]"></a>MemManage_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.MemManage_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MemManage_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MemManage_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[14e]"></a>Motor_Create</STRONG> (Thumb, 144 bytes, Stack size 24 bytes, motor_driver_tb6612.o(i.Motor_Create))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = Motor_Create &rArr; HAL_TIM_PWM_Start &rArr; TIM_CCxChannelCmd
</UL>
<BR>[Calls]<UL><LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
<LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_PWM_Start
</UL>
<BR>[Called By]<UL><LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;motor_init
</UL>

<P><STRONG><a name="[13d]"></a>Motor_SetSpeed</STRONG> (Thumb, 288 bytes, Stack size 16 bytes, motor_driver_tb6612.o(i.Motor_SetSpeed))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = Motor_SetSpeed
</UL>
<BR>[Calls]<UL><LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
<LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Speed_To_PWM
<LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Motor_ValidateParams
</UL>
<BR>[Called By]<UL><LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Location_Speed_control
</UL>

<P><STRONG><a name="[c]"></a>NMI_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.NMI_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NMI_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NMI_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[151]"></a>OLED_Clear</STRONG> (Thumb, 52 bytes, Stack size 16 bytes, oled.o(i.OLED_Clear))
<BR><BR>[Stack]<UL><LI>Max Depth = 168<LI>Call Chain = OLED_Clear &rArr; OLED_Write_data &rArr; HAL_I2C_Mem_Write &rArr; I2C_RequestMemoryWrite &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Write_data
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Write_cmd
</UL>
<BR>[Called By]<UL><LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
</UL>

<P><STRONG><a name="[154]"></a>OLED_Init</STRONG> (Thumb, 42 bytes, Stack size 16 bytes, oled.o(i.OLED_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 184<LI>Call Chain = OLED_Init &rArr; OLED_Clear &rArr; OLED_Write_data &rArr; HAL_I2C_Mem_Write &rArr; I2C_RequestMemoryWrite &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Write_cmd
<LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Set_Position
<LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Clear
</UL>
<BR>[Called By]<UL><LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[155]"></a>OLED_Set_Position</STRONG> (Thumb, 34 bytes, Stack size 8 bytes, oled.o(i.OLED_Set_Position))
<BR><BR>[Stack]<UL><LI>Max Depth = 160<LI>Call Chain = OLED_Set_Position &rArr; OLED_Write_cmd &rArr; HAL_I2C_Mem_Write &rArr; I2C_RequestMemoryWrite &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Write_cmd
</UL>
<BR>[Called By]<UL><LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
</UL>

<P><STRONG><a name="[152]"></a>OLED_Write_cmd</STRONG> (Thumb, 32 bytes, Stack size 24 bytes, oled.o(i.OLED_Write_cmd))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = OLED_Write_cmd &rArr; HAL_I2C_Mem_Write &rArr; I2C_RequestMemoryWrite &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Mem_Write
</UL>
<BR>[Called By]<UL><LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
<LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Set_Position
<LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Clear
</UL>

<P><STRONG><a name="[153]"></a>OLED_Write_data</STRONG> (Thumb, 32 bytes, Stack size 24 bytes, oled.o(i.OLED_Write_data))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = OLED_Write_data &rArr; HAL_I2C_Mem_Write &rArr; I2C_RequestMemoryWrite &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Mem_Write
</UL>
<BR>[Called By]<UL><LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Clear
</UL>

<P><STRONG><a name="[156]"></a>PID_INIT</STRONG> (Thumb, 182 bytes, Stack size 16 bytes, mypid.o(i.PID_INIT))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = PID_INIT &rArr; PID_struct_init
</UL>
<BR>[Calls]<UL><LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PID_struct_init
</UL>
<BR>[Called By]<UL><LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[157]"></a>PID_struct_init</STRONG> (Thumb, 60 bytes, Stack size 24 bytes, mypid.o(i.PID_struct_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = PID_struct_init
</UL>
<BR>[Calls]<UL><LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pid_param_init
</UL>
<BR>[Called By]<UL><LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PID_INIT
</UL>

<P><STRONG><a name="[13]"></a>PendSV_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.PendSV_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[11]"></a>SVC_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.SVC_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[158]"></a>Step_Motor_Init</STRONG> (Thumb, 34 bytes, Stack size 8 bytes, step_motor_bsp.o(i.Step_Motor_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = Step_Motor_Init &rArr; Step_Motor_Stop &rArr; Emm_V5_Stop_Now &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Emm_V5_En_Control
<LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Step_Motor_Stop
</UL>
<BR>[Called By]<UL><LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[15a]"></a>Step_Motor_Set_Pwm</STRONG> (Thumb, 76 bytes, Stack size 40 bytes, step_motor_bsp.o(i.Step_Motor_Set_Pwm))
<BR><BR>[Stack]<UL><LI>Max Depth = 144<LI>Call Chain = Step_Motor_Set_Pwm &rArr; Emm_V5_Pos_Control &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Emm_V5_Pos_Control
</UL>
<BR>[Called By]<UL><LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[15b]"></a>Step_Motor_Set_Speed_my</STRONG> (Thumb, 178 bytes, Stack size 24 bytes, step_motor_bsp.o(i.Step_Motor_Set_Speed_my))
<BR><BR>[Stack]<UL><LI>Max Depth = 728 + Unknown Stack Size
<LI>Call Chain = Step_Motor_Set_Speed_my &rArr; Emm_V5_Vel_Control &rArr; my_printf &rArr; vsnprintf &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Emm_V5_Vel_Control
</UL>
<BR>[Called By]<UL><LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pi_proc
</UL>

<P><STRONG><a name="[159]"></a>Step_Motor_Stop</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, step_motor_bsp.o(i.Step_Motor_Stop))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = Step_Motor_Stop &rArr; Emm_V5_Stop_Now &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Emm_V5_Stop_Now
</UL>
<BR>[Called By]<UL><LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Step_Motor_Init
</UL>

<P><STRONG><a name="[14]"></a>SysTick_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.SysTick_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_IncTick
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[15d]"></a>SystemClock_Config</STRONG> (Thumb, 136 bytes, Stack size 88 bytes, main.o(i.SystemClock_Config))
<BR><BR>[Stack]<UL><LI>Max Depth = 176<LI>Call Chain = SystemClock_Config &rArr; HAL_RCC_ClockConfig &rArr; HAL_RCC_GetSysClockFreq &rArr; __aeabi_uldivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_OscConfig
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_ClockConfig
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[66]"></a>SystemInit</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, system_stm32f4xx.o(i.SystemInit))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(.text)
</UL>
<P><STRONG><a name="[115]"></a>TIM_Base_SetConfig</STRONG> (Thumb, 164 bytes, Stack size 20 bytes, stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = TIM_Base_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_PWM_Init
<LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Encoder_Init
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_Init
</UL>

<P><STRONG><a name="[11d]"></a>TIM_CCxChannelCmd</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = TIM_CCxChannelCmd
</UL>
<BR>[Called By]<UL><LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_PWM_Start
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Encoder_Start
</UL>

<P><STRONG><a name="[117]"></a>TIM_ETR_SetConfig</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = TIM_ETR_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_ConfigClockSource
</UL>

<P><STRONG><a name="[121]"></a>TIM_OC2_SetConfig</STRONG> (Thumb, 98 bytes, Stack size 20 bytes, stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = TIM_OC2_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_PWM_ConfigChannel
</UL>

<P><STRONG><a name="[49]"></a>UART4_IRQHandler</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, stm32f4xx_it.o(i.UART4_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 120 + Unknown Stack Size
<LI>Call Chain = UART4_IRQHandler &rArr; HAL_UART_IRQHandler &rArr; UART_Receive_IT &rArr; HAL_UARTEx_RxEventCallback &rArr; rt_ringbuffer_put &rArr; abort &rArr; __rt_SIGABRT &rArr; __rt_SIGABRT_inner &rArr; __default_signal_display &rArr; _ttywrch
</UL>
<BR>[Calls]<UL><LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
<LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_ReceiveToIdle_DMA
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[128]"></a>UART_Start_Receive_DMA</STRONG> (Thumb, 146 bytes, Stack size 16 bytes, stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = UART_Start_Receive_DMA &rArr; HAL_DMA_Start_IT &rArr; DMA_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Start_IT
</UL>
<BR>[Called By]<UL><LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_ReceiveToIdle_DMA
</UL>

<P><STRONG><a name="[3a]"></a>USART1_IRQHandler</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.USART1_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 112 + Unknown Stack Size
<LI>Call Chain = USART1_IRQHandler &rArr; HAL_UART_IRQHandler &rArr; UART_Receive_IT &rArr; HAL_UARTEx_RxEventCallback &rArr; rt_ringbuffer_put &rArr; abort &rArr; __rt_SIGABRT &rArr; __rt_SIGABRT_inner &rArr; __default_signal_display &rArr; _ttywrch
</UL>
<BR>[Calls]<UL><LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[3b]"></a>USART2_IRQHandler</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, stm32f4xx_it.o(i.USART2_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 120 + Unknown Stack Size
<LI>Call Chain = USART2_IRQHandler &rArr; HAL_UART_IRQHandler &rArr; UART_Receive_IT &rArr; HAL_UARTEx_RxEventCallback &rArr; rt_ringbuffer_put &rArr; abort &rArr; __rt_SIGABRT &rArr; __rt_SIGABRT_inner &rArr; __default_signal_display &rArr; _ttywrch
</UL>
<BR>[Calls]<UL><LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
<LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_ReceiveToIdle_DMA
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[3c]"></a>USART3_IRQHandler</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.USART3_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 112 + Unknown Stack Size
<LI>Call Chain = USART3_IRQHandler &rArr; HAL_UART_IRQHandler &rArr; UART_Receive_IT &rArr; HAL_UARTEx_RxEventCallback &rArr; rt_ringbuffer_put &rArr; abort &rArr; __rt_SIGABRT &rArr; __rt_SIGABRT_inner &rArr; __default_signal_display &rArr; _ttywrch
</UL>
<BR>[Calls]<UL><LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[5c]"></a>USART6_IRQHandler</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, stm32f4xx_it.o(i.USART6_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 120 + Unknown Stack Size
<LI>Call Chain = USART6_IRQHandler &rArr; HAL_UART_IRQHandler &rArr; UART_Receive_IT &rArr; HAL_UARTEx_RxEventCallback &rArr; rt_ringbuffer_put &rArr; abort &rArr; __rt_SIGABRT &rArr; __rt_SIGABRT_inner &rArr; __default_signal_display &rArr; _ttywrch
</UL>
<BR>[Calls]<UL><LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
<LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_ReceiveToIdle_DMA
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[10]"></a>UsageFault_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.UsageFault_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[10]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsageFault_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[10]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsageFault_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[d9]"></a>__ARM_fpclassify</STRONG> (Thumb, 48 bytes, Stack size 8 bytes, fpclassify.o(i.__ARM_fpclassify))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __ARM_fpclassify
</UL>
<BR>[Called By]<UL><LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_hex_real
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_dec_real
</UL>

<P><STRONG><a name="[bc]"></a>_is_digit</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, __printf_wp.o(i._is_digit))
<BR><BR>[Called By]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__printf
</UL>

<P><STRONG><a name="[173]"></a>abs_limit</STRONG> (Thumb, 42 bytes, Stack size 0 bytes, mypid.o(i.abs_limit))
<BR><BR>[Called By]<UL><LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pid_calc
</UL>

<P><STRONG><a name="[16d]"></a>calc_motor_angle</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, uart_bsp.o(i.calc_motor_angle))
<BR><BR>[Called By]<UL><LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parse_y_motor_data
<LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parse_x_motor_data
</UL>

<P><STRONG><a name="[16e]"></a>calc_relative_angle</STRONG> (Thumb, 62 bytes, Stack size 0 bytes, uart_bsp.o(i.calc_relative_angle))
<BR><BR>[Called By]<UL><LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parse_y_motor_data
<LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parse_x_motor_data
</UL>

<P><STRONG><a name="[162]"></a>check_motor_angle_limits</STRONG> (Thumb, 114 bytes, Stack size 24 bytes, uart_bsp.o(i.check_motor_angle_limits))
<BR><BR>[Stack]<UL><LI>Max Depth = 696 + Unknown Stack Size
<LI>Call Chain = check_motor_angle_limits &rArr; my_printf &rArr; vsnprintf &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Emm_V5_Stop_Now
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
</UL>
<BR>[Called By]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_proc
</UL>

<P><STRONG><a name="[163]"></a>encoder_init</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, encoder_bsp.o(i.encoder_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = encoder_init &rArr; Encoder_Driver_Init &rArr; HAL_TIM_Encoder_Start &rArr; TIM_CCxChannelCmd
</UL>
<BR>[Calls]<UL><LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Encoder_Driver_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[79]"></a>encoder_proc</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, encoder_bsp.o(i.encoder_proc))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = encoder_proc
</UL>
<BR>[Calls]<UL><LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Encoder_Driver_Update
</UL>
<BR>[Address Reference Count : 1]<UL><LI> schedule.o(.data)
</UL>
<P><STRONG><a name="[7b]"></a>gray_proc</STRONG> (Thumb, 66 bytes, Stack size 32 bytes, gray_bsp.o(i.gray_proc))
<BR><BR>[Stack]<UL><LI>Max Depth = 704 + Unknown Stack Size
<LI>Call Chain = gray_proc &rArr; my_printf &rArr; vsnprintf &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_Get_Digtal
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> schedule.o(.data)
</UL>
<P><STRONG><a name="[7a]"></a>key_proc</STRONG> (Thumb, 82 bytes, Stack size 8 bytes, key_bsp.o(i.key_proc))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = key_proc &rArr; key_read
</UL>
<BR>[Calls]<UL><LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;key_read
</UL>
<BR>[Address Reference Count : 1]<UL><LI> schedule.o(.data)
</UL>
<P><STRONG><a name="[165]"></a>key_read</STRONG> (Thumb, 58 bytes, Stack size 16 bytes, key_bsp.o(i.key_read))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = key_read
</UL>
<BR>[Calls]<UL><LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_ReadPin
</UL>
<BR>[Called By]<UL><LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;key_proc
</UL>

<P><STRONG><a name="[af]"></a>main</STRONG> (Thumb, 162 bytes, Stack size 0 bytes, main.o(i.main))
<BR><BR>[Stack]<UL><LI>Max Depth = 728 + Unknown Stack Size
<LI>Call Chain = main &rArr; Step_Motor_Set_Speed_my &rArr; Emm_V5_Vel_Control &rArr; my_printf &rArr; vsnprintf &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;schedule_run
<LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;schedule_init
<LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;save_initial_position
<LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_ringbuffer_init
<LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;motor_init
<LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;encoder_init
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Step_Motor_Set_Speed_my
<LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Step_Motor_Set_Pwm
<LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Step_Motor_Init
<LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PID_INIT
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
<LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART6_UART_Init
<LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART3_UART_Init
<LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART2_UART_Init
<LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART1_UART_Init
<LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_UART5_Init
<LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_UART4_Init
<LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM4_Init
<LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM3_Init
<LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM1_Init
<LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_I2C2_Init
<LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_I2C1_Init
<LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_GPIO_Init
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_DMA_Init
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Init
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Emm_V5_Reset_CurPos_To_Zero
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
</UL>
<BR>[Called By]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_main
</UL>

<P><STRONG><a name="[168]"></a>motor_init</STRONG> (Thumb, 58 bytes, Stack size 32 bytes, motor_bsp.o(i.motor_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = motor_init &rArr; Motor_Create &rArr; HAL_TIM_PWM_Start &rArr; TIM_CCxChannelCmd
</UL>
<BR>[Calls]<UL><LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Motor_Create
</UL>
<BR>[Called By]<UL><LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[78]"></a>motor_proc</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, motor_bsp.o(i.motor_proc))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = motor_proc &rArr; Location_Speed_control &rArr; Motor_SetSpeed
</UL>
<BR>[Calls]<UL><LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Location_Speed_control
</UL>
<BR>[Address Reference Count : 1]<UL><LI> schedule.o(.data)
</UL>
<P><STRONG><a name="[f1]"></a>my_printf</STRONG> (Thumb, 52 bytes, Stack size 544 bytes, uart_bsp.o(i.my_printf))
<BR><BR>[Stack]<UL><LI>Max Depth = 672 + Unknown Stack Size
<LI>Call Chain = my_printf &rArr; vsnprintf &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Transmit
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vsnprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;save_initial_position
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Emm_V5_Vel_Control
<LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pi_parse_data
<LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parse_y_motor_data
<LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parse_x_motor_data
<LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;check_motor_angle_limits
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_proc
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gray_proc
</UL>

<P><STRONG><a name="[75]"></a>oled_proc</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, oled_bsp.o(i.oled_proc))
<BR>[Address Reference Count : 1]<UL><LI> schedule.o(.data)
</UL>
<P><STRONG><a name="[16c]"></a>parse_x_motor_data</STRONG> (Thumb, 326 bytes, Stack size 40 bytes, uart_bsp.o(i.parse_x_motor_data))
<BR><BR>[Stack]<UL><LI>Max Depth = 712 + Unknown Stack Size
<LI>Call Chain = parse_x_motor_data &rArr; my_printf &rArr; vsnprintf &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;calc_relative_angle
<LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;calc_motor_angle
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
</UL>
<BR>[Called By]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_proc
</UL>

<P><STRONG><a name="[170]"></a>parse_y_motor_data</STRONG> (Thumb, 326 bytes, Stack size 40 bytes, uart_bsp.o(i.parse_y_motor_data))
<BR><BR>[Stack]<UL><LI>Max Depth = 712 + Unknown Stack Size
<LI>Call Chain = parse_y_motor_data &rArr; my_printf &rArr; vsnprintf &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;calc_relative_angle
<LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;calc_motor_angle
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
</UL>
<BR>[Called By]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_proc
</UL>

<P><STRONG><a name="[171]"></a>pi_parse_data</STRONG> (Thumb, 122 bytes, Stack size 24 bytes, pi_bsp.o(i.pi_parse_data))
<BR><BR>[Stack]<UL><LI>Max Depth = 696 + Unknown Stack Size
<LI>Call Chain = pi_parse_data &rArr; my_printf &rArr; vsnprintf &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strncmp
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0sscanf
</UL>
<BR>[Called By]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_proc
</UL>

<P><STRONG><a name="[77]"></a>pi_proc</STRONG> (Thumb, 84 bytes, Stack size 24 bytes, pi_bsp.o(i.pi_proc))
<BR><BR>[Stack]<UL><LI>Max Depth = 752 + Unknown Stack Size
<LI>Call Chain = pi_proc &rArr; Step_Motor_Set_Speed_my &rArr; Emm_V5_Vel_Control &rArr; my_printf &rArr; vsnprintf &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Step_Motor_Set_Speed_my
<LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pid_calc
</UL>
<BR>[Address Reference Count : 1]<UL><LI> schedule.o(.data)
</UL>
<P><STRONG><a name="[13c]"></a>pid_calc</STRONG> (Thumb, 390 bytes, Stack size 4 bytes, mypid.o(i.pid_calc))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = pid_calc
</UL>
<BR>[Calls]<UL><LI><a href="#[173]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;abs_limit
</UL>
<BR>[Called By]<UL><LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Location_Speed_control
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pi_proc
</UL>

<P><STRONG><a name="[174]"></a>rt_ringbuffer_data_len</STRONG> (Thumb, 48 bytes, Stack size 8 bytes, ringbuffer.o(i.rt_ringbuffer_data_len))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = rt_ringbuffer_data_len
</UL>
<BR>[Calls]<UL><LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_ringbuffer_status
</UL>
<BR>[Called By]<UL><LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_ringbuffer_put
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_proc
<LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_ringbuffer_get
</UL>

<P><STRONG><a name="[176]"></a>rt_ringbuffer_get</STRONG> (Thumb, 116 bytes, Stack size 16 bytes, ringbuffer.o(i.rt_ringbuffer_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 64 + Unknown Stack Size
<LI>Call Chain = rt_ringbuffer_get &rArr; abort &rArr; __rt_SIGABRT &rArr; __rt_SIGABRT_inner &rArr; __default_signal_display &rArr; _ttywrch
</UL>
<BR>[Calls]<UL><LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_ringbuffer_data_len
<LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;abort
</UL>
<BR>[Called By]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_proc
</UL>

<P><STRONG><a name="[169]"></a>rt_ringbuffer_init</STRONG> (Thumb, 48 bytes, Stack size 8 bytes, ringbuffer.o(i.rt_ringbuffer_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 56 + Unknown Stack Size
<LI>Call Chain = rt_ringbuffer_init &rArr; abort &rArr; __rt_SIGABRT &rArr; __rt_SIGABRT_inner &rArr; __default_signal_display &rArr; _ttywrch
</UL>
<BR>[Calls]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;abort
</UL>
<BR>[Called By]<UL><LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[12a]"></a>rt_ringbuffer_put</STRONG> (Thumb, 120 bytes, Stack size 16 bytes, ringbuffer.o(i.rt_ringbuffer_put))
<BR><BR>[Stack]<UL><LI>Max Depth = 64 + Unknown Stack Size
<LI>Call Chain = rt_ringbuffer_put &rArr; abort &rArr; __rt_SIGABRT &rArr; __rt_SIGABRT_inner &rArr; __default_signal_display &rArr; _ttywrch
</UL>
<BR>[Calls]<UL><LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_ringbuffer_data_len
<LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;abort
</UL>
<BR>[Called By]<UL><LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_RxEventCallback
</UL>

<P><STRONG><a name="[16a]"></a>save_initial_position</STRONG> (Thumb, 44 bytes, Stack size 8 bytes, uart_bsp.o(i.save_initial_position))
<BR><BR>[Stack]<UL><LI>Max Depth = 680 + Unknown Stack Size
<LI>Call Chain = save_initial_position &rArr; my_printf &rArr; vsnprintf &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Emm_V5_Read_Sys_Params
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
</UL>
<BR>[Called By]<UL><LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[167]"></a>schedule_init</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, schedule.o(i.schedule_init))
<BR><BR>[Called By]<UL><LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[16b]"></a>schedule_run</STRONG> (Thumb, 56 bytes, Stack size 24 bytes, schedule.o(i.schedule_run))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = schedule_run
</UL>
<BR>[Calls]<UL><LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[76]"></a>uart_proc</STRONG> (Thumb, 282 bytes, Stack size 216 bytes, uart_bsp.o(i.uart_proc))
<BR><BR>[Stack]<UL><LI>Max Depth = 928 + Unknown Stack Size
<LI>Call Chain = uart_proc &rArr; parse_y_motor_data &rArr; my_printf &rArr; vsnprintf &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pi_parse_data
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Emm_V5_Parse_Response
<LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parse_y_motor_data
<LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parse_x_motor_data
<LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;check_motor_angle_limits
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_ringbuffer_get
<LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_ringbuffer_data_len
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Address Reference Count : 1]<UL><LI> schedule.o(.data)
</UL>
<P><STRONG><a name="[a9]"></a>_get_lc_numeric</STRONG> (Thumb, 44 bytes, Stack size 8 bytes, lc_numeric_c.o(locale$$code))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = _get_lc_numeric
</UL>
<BR>[Calls]<UL><LI><a href="#[178]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcmp
</UL>
<BR>[Called By]<UL><LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_init_lc_numeric_2
</UL>

<P><STRONG><a name="[6e]"></a>_get_lc_ctype</STRONG> (Thumb, 44 bytes, Stack size 8 bytes, lc_ctype_c.o(locale$$code))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = _get_lc_ctype
</UL>
<BR>[Calls]<UL><LI><a href="#[178]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcmp
</UL>
<BR>[Called By]<UL><LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_init_lc_ctype_2
</UL>
<BR>[Address Reference Count : 1]<UL><LI> rt_ctype_table.o(.text)
</UL>
<P><STRONG><a name="[17b]"></a>__fpl_dretinf</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, dretinf.o(x$fpl$dretinf), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_f2d
</UL>

<P><STRONG><a name="[16f]"></a>__aeabi_f2d</STRONG> (Thumb, 0 bytes, Stack size 16 bytes, f2d.o(x$fpl$f2d))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __aeabi_f2d
</UL>
<BR>[Called By]<UL><LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parse_y_motor_data
<LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parse_x_motor_data
</UL>

<P><STRONG><a name="[179]"></a>_f2d</STRONG> (Thumb, 86 bytes, Stack size 16 bytes, f2d.o(x$fpl$f2d), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_fnaninf
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dretinf
</UL>

<P><STRONG><a name="[17a]"></a>__fpl_fnaninf</STRONG> (Thumb, 140 bytes, Stack size 8 bytes, fnaninf.o(x$fpl$fnaninf), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_f2d
</UL>

<P><STRONG><a name="[a4]"></a>_fp_init</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, fpinit.o(x$fpl$fpinit))
<BR><BR>[Called By]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_init_fp_1
</UL>

<P><STRONG><a name="[1b1]"></a>__fplib_config_fpu_vfp</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, fpinit.o(x$fpl$fpinit), UNUSED)

<P><STRONG><a name="[1b2]"></a>__fplib_config_pureend_doubles</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, fpinit.o(x$fpl$fpinit), UNUSED)

<P><STRONG><a name="[86]"></a>_printf_fp_dec</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, printf1.o(x$fpl$printf1))
<BR><BR>[Stack]<UL><LI>Max Depth = 324<LI>Call Chain = _printf_fp_dec &rArr; _printf_fp_dec_real &rArr; _fp_digits &rArr; _btod_etento &rArr; _btod_emul &rArr; _e2e
</UL>
<BR>[Calls]<UL><LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_dec_real
</UL>
<BR>[Called By]<UL><LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_g
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_e
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_f
</UL>

<P><STRONG><a name="[8a]"></a>_printf_fp_hex</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, printf2.o(x$fpl$printf2))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = _printf_fp_hex &rArr; _printf_fp_hex_real &rArr; _printf_fp_infnan &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_hex_real
</UL>
<BR>[Called By]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_a
</UL>
<P>
<H3>
Local Symbols
</H3>
<P><STRONG><a name="[138]"></a>I2C_IsAcknowledgeFailed</STRONG> (Thumb, 46 bytes, Stack size 0 bytes, stm32f4xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed))
<BR><BR>[Called By]<UL><LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnTXEFlagUntilTimeout
<LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnBTFFlagUntilTimeout
</UL>

<P><STRONG><a name="[101]"></a>I2C_RequestMemoryRead</STRONG> (Thumb, 246 bytes, Stack size 40 bytes, stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryRead))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = I2C_RequestMemoryRead &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnTXEFlagUntilTimeout
<LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnMasterAddressFlagUntilTimeout
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnFlagUntilTimeout
</UL>
<BR>[Called By]<UL><LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Mem_Read
</UL>

<P><STRONG><a name="[104]"></a>I2C_RequestMemoryWrite</STRONG> (Thumb, 162 bytes, Stack size 32 bytes, stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryWrite))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = I2C_RequestMemoryWrite &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnTXEFlagUntilTimeout
<LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnMasterAddressFlagUntilTimeout
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnFlagUntilTimeout
</UL>
<BR>[Called By]<UL><LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Mem_Write
</UL>

<P><STRONG><a name="[106]"></a>I2C_WaitOnBTFFlagUntilTimeout</STRONG> (Thumb, 86 bytes, Stack size 16 bytes, stm32f4xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = I2C_WaitOnBTFFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_IsAcknowledgeFailed
</UL>
<BR>[Called By]<UL><LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Mem_Write
</UL>

<P><STRONG><a name="[100]"></a>I2C_WaitOnFlagUntilTimeout</STRONG> (Thumb, 144 bytes, Stack size 32 bytes, stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = I2C_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Mem_Write
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Mem_Read
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_RequestMemoryWrite
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_RequestMemoryRead
</UL>

<P><STRONG><a name="[137]"></a>I2C_WaitOnMasterAddressFlagUntilTimeout</STRONG> (Thumb, 188 bytes, Stack size 32 bytes, stm32f4xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_RequestMemoryWrite
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_RequestMemoryRead
</UL>

<P><STRONG><a name="[102]"></a>I2C_WaitOnRXNEFlagUntilTimeout</STRONG> (Thumb, 112 bytes, Stack size 24 bytes, stm32f4xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = I2C_WaitOnRXNEFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Mem_Read
</UL>

<P><STRONG><a name="[105]"></a>I2C_WaitOnTXEFlagUntilTimeout</STRONG> (Thumb, 86 bytes, Stack size 16 bytes, stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = I2C_WaitOnTXEFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_IsAcknowledgeFailed
</UL>
<BR>[Called By]<UL><LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Mem_Write
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_RequestMemoryWrite
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_RequestMemoryRead
</UL>

<P><STRONG><a name="[f8]"></a>DMA_CalcBaseAndBitshift</STRONG> (Thumb, 34 bytes, Stack size 0 bytes, stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift))
<BR><BR>[Called By]<UL><LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Init
</UL>

<P><STRONG><a name="[f7]"></a>DMA_CheckFifoParam</STRONG> (Thumb, 84 bytes, Stack size 8 bytes, stm32f4xx_hal_dma.o(i.DMA_CheckFifoParam))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DMA_CheckFifoParam
</UL>
<BR>[Called By]<UL><LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Init
</UL>

<P><STRONG><a name="[fa]"></a>DMA_SetConfig</STRONG> (Thumb, 40 bytes, Stack size 12 bytes, stm32f4xx_hal_dma.o(i.DMA_SetConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = DMA_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Start_IT
</UL>

<P><STRONG><a name="[10e]"></a>__NVIC_SetPriority</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, stm32f4xx_hal_cortex.o(i.__NVIC_SetPriority))
<BR><BR>[Called By]<UL><LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
<LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SYSTICK_Config
</UL>

<P><STRONG><a name="[11a]"></a>TIM_ITRx_SetConfig</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, stm32f4xx_hal_tim.o(i.TIM_ITRx_SetConfig))
<BR><BR>[Called By]<UL><LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_ConfigClockSource
</UL>

<P><STRONG><a name="[120]"></a>TIM_OC1_SetConfig</STRONG> (Thumb, 88 bytes, Stack size 20 bytes, stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = TIM_OC1_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_PWM_ConfigChannel
</UL>

<P><STRONG><a name="[122]"></a>TIM_OC3_SetConfig</STRONG> (Thumb, 96 bytes, Stack size 20 bytes, stm32f4xx_hal_tim.o(i.TIM_OC3_SetConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = TIM_OC3_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_PWM_ConfigChannel
</UL>

<P><STRONG><a name="[123]"></a>TIM_OC4_SetConfig</STRONG> (Thumb, 70 bytes, Stack size 12 bytes, stm32f4xx_hal_tim.o(i.TIM_OC4_SetConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = TIM_OC4_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_PWM_ConfigChannel
</UL>

<P><STRONG><a name="[118]"></a>TIM_TI1_ConfigInputStage</STRONG> (Thumb, 34 bytes, Stack size 8 bytes, stm32f4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = TIM_TI1_ConfigInputStage
</UL>
<BR>[Called By]<UL><LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_ConfigClockSource
</UL>

<P><STRONG><a name="[119]"></a>TIM_TI2_ConfigInputStage</STRONG> (Thumb, 36 bytes, Stack size 8 bytes, stm32f4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = TIM_TI2_ConfigInputStage
</UL>
<BR>[Called By]<UL><LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_ConfigClockSource
</UL>

<P><STRONG><a name="[6f]"></a>UART_DMAAbortOnError</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = UART_DMAAbortOnError
</UL>
<BR>[Calls]<UL><LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_ErrorCallback
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler)
</UL>
<P><STRONG><a name="[74]"></a>UART_DMAError</STRONG> (Thumb, 74 bytes, Stack size 16 bytes, stm32f4xx_hal_uart.o(i.UART_DMAError))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = UART_DMAError
</UL>
<BR>[Calls]<UL><LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_ErrorCallback
<LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_EndTxTransfer
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_EndRxTransfer
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA)
</UL>
<P><STRONG><a name="[72]"></a>UART_DMAReceiveCplt</STRONG> (Thumb, 134 bytes, Stack size 8 bytes, stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt))
<BR><BR>[Stack]<UL><LI>Max Depth = 88 + Unknown Stack Size
<LI>Call Chain = UART_DMAReceiveCplt &rArr; HAL_UARTEx_RxEventCallback &rArr; rt_ringbuffer_put &rArr; abort &rArr; __rt_SIGABRT &rArr; __rt_SIGABRT_inner &rArr; __default_signal_display &rArr; _ttywrch
</UL>
<BR>[Calls]<UL><LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_RxEventCallback
<LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_RxCpltCallback
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA)
</UL>
<P><STRONG><a name="[73]"></a>UART_DMARxHalfCplt</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt))
<BR><BR>[Stack]<UL><LI>Max Depth = 88 + Unknown Stack Size
<LI>Call Chain = UART_DMARxHalfCplt &rArr; HAL_UARTEx_RxEventCallback &rArr; rt_ringbuffer_put &rArr; abort &rArr; __rt_SIGABRT &rArr; __rt_SIGABRT_inner &rArr; __default_signal_display &rArr; _ttywrch
</UL>
<BR>[Calls]<UL><LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_RxEventCallback
<LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_RxHalfCpltCallback
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA)
</UL>
<P><STRONG><a name="[12d]"></a>UART_EndRxTransfer</STRONG> (Thumb, 78 bytes, Stack size 0 bytes, stm32f4xx_hal_uart.o(i.UART_EndRxTransfer))
<BR><BR>[Calls]<UL><LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_EndRxTransfer
</UL>
<BR>[Called By]<UL><LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
<LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_WaitOnFlagUntilTimeout
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_EndRxTransfer
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_DMAError
</UL>

<P><STRONG><a name="[15e]"></a>UART_EndTxTransfer</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, stm32f4xx_hal_uart.o(i.UART_EndTxTransfer))
<BR><BR>[Calls]<UL><LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_EndTxTransfer
</UL>
<BR>[Called By]<UL><LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_EndTxTransfer
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_DMAError
</UL>

<P><STRONG><a name="[12c]"></a>UART_Receive_IT</STRONG> (Thumb, 194 bytes, Stack size 8 bytes, stm32f4xx_hal_uart.o(i.UART_Receive_IT))
<BR><BR>[Stack]<UL><LI>Max Depth = 88 + Unknown Stack Size
<LI>Call Chain = UART_Receive_IT &rArr; HAL_UARTEx_RxEventCallback &rArr; rt_ringbuffer_put &rArr; abort &rArr; __rt_SIGABRT &rArr; __rt_SIGABRT_inner &rArr; __default_signal_display &rArr; _ttywrch
</UL>
<BR>[Calls]<UL><LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_RxEventCallback
<LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_RxCpltCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
</UL>

<P><STRONG><a name="[133]"></a>UART_SetConfig</STRONG> (Thumb, 258 bytes, Stack size 16 bytes, stm32f4xx_hal_uart.o(i.UART_SetConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = UART_SetConfig &rArr; __aeabi_uldivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetPCLK2Freq
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetPCLK1Freq
<LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
</UL>
<BR>[Called By]<UL><LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Init
</UL>

<P><STRONG><a name="[136]"></a>UART_WaitOnFlagUntilTimeout</STRONG> (Thumb, 114 bytes, Stack size 32 bytes, stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_EndRxTransfer
</UL>
<BR>[Called By]<UL><LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Transmit
</UL>

<P><STRONG><a name="[175]"></a>rt_ringbuffer_status</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, ringbuffer.o(i.rt_ringbuffer_status))
<BR><BR>[Called By]<UL><LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_ringbuffer_data_len
</UL>

<P><STRONG><a name="[70]"></a>pid_param_init</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, mypid.o(i.pid_param_init))
<BR><BR>[Called By]<UL><LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PID_struct_init
</UL>
<BR>[Address Reference Count : 1]<UL><LI> mypid.o(i.PID_struct_init)
</UL>
<P><STRONG><a name="[71]"></a>pid_reset</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, mypid.o(i.pid_reset))
<BR>[Address Reference Count : 1]<UL><LI> mypid.o(i.PID_struct_init)
</UL>
<P><STRONG><a name="[14f]"></a>Motor_ValidateParams</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, motor_driver_tb6612.o(i.Motor_ValidateParams))
<BR><BR>[Called By]<UL><LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Motor_SetSpeed
</UL>

<P><STRONG><a name="[150]"></a>Speed_To_PWM</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, motor_driver_tb6612.o(i.Speed_To_PWM))
<BR><BR>[Called By]<UL><LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Motor_SetSpeed
</UL>

<P><STRONG><a name="[6b]"></a>_printf_input_char</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, _printf_char_common.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> _printf_char_common.o(.text)
</UL>
<P><STRONG><a name="[6c]"></a>_scanf_char_input</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, scanf_char.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> scanf_char.o(.text)
</UL>
<P><STRONG><a name="[d3]"></a>_fp_digits</STRONG> (Thumb, 432 bytes, Stack size 96 bytes, _printf_fp_dec.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 220<LI>Call Chain = _fp_digits &rArr; _btod_etento &rArr; _btod_emul &rArr; _e2e
</UL>
<BR>[Calls]<UL><LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_ll_udiv10
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_emul
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_ediv
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_d2e
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_etento
</UL>
<BR>[Called By]<UL><LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_dec_real
</UL>
<P>
<H3>
Undefined Global Symbols
</H3><HR></body></html>
